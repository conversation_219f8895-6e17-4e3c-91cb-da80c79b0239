{"name": "ymMp", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": false, "css": "none"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@tarojs/components": "3.5.1", "@tarojs/helper": "3.5.1", "@tarojs/plugin-framework-vue3": "3.5.0", "@tarojs/plugin-platform-alipay": "3.5.1", "@tarojs/plugin-platform-jd": "3.5.1", "@tarojs/plugin-platform-qq": "3.5.1", "@tarojs/plugin-platform-swan": "3.5.1", "@tarojs/plugin-platform-tt": "3.5.1", "@tarojs/plugin-platform-weapp": "3.5.1", "@tarojs/router": "3.5.1", "@tarojs/runtime": "3.5.1", "@tarojs/shared": "3.5.1", "@tarojs/taro": "3.5.1", "@tarojs/taro-h5": "3.5.1", "minidev": "^1.5.1", "miniprogram-ci": "^1.8.35", "swan-toolkit": "^3.10.2", "tt-ide-cli": "^0.1.15", "vue": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "3.5.1", "@tarojs/webpack5-runner": "3.5.0", "@types/webpack-env": "^1.13.6", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.0.0", "babel-preset-taro": "3.5.1", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.5.1", "eslint-plugin-vue": "^8.0.0", "style-loader": "1.3.0", "stylelint": "^14.4.0", "vue-loader": "^16.0.0-beta.8", "webpack": "5.69.0"}}