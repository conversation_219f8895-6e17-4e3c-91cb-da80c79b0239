<template>
  <view class="search-container">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="back-btn" @click="goBack">←</view>
      <view class="search-input-container">
        <input 
          class="search-input" 
          placeholder="请输入您要查找的轴承型号"
          v-model="searchText"
          @input="handleInput"
          focus
        />
        <view class="search-btn" @click="handleSearch">搜索</view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="search-history" v-if="searchHistory.length > 0 && !searchText">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      <view class="history-tags">
        <view 
          class="history-tag" 
          v-for="(item, index) in searchHistory" 
          :key="index"
          @click="selectHistory(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="hot-search" v-if="!searchText">
      <view class="hot-title">热门搜索</view>
      <view class="hot-tags">
        <view 
          class="hot-tag" 
          v-for="(item, index) in hotSearches" 
          :key="index"
          @click="selectHotSearch(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results" v-if="searchText && searchResults.length > 0">
      <view class="result-item" v-for="(item, index) in searchResults" :key="index">
        <view class="result-title">{{ item.title }}</view>
        <view class="result-desc">{{ item.description }}</view>
        <view class="result-price">¥{{ item.price }}</view>
      </view>
    </view>

    <!-- 无搜索结果 -->
    <view class="no-results" v-if="searchText && searchResults.length === 0 && hasSearched">
      <view class="no-results-icon">🔍</view>
      <view class="no-results-text">未找到相关轴承型号</view>
      <view class="no-results-tip">请尝试其他关键词</view>
    </view>
  </view>
</template>

<script>
import { reactive, ref } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const searchText = ref('');
    const hasSearched = ref(false);
    
    const searchHistory = reactive([
      '6205', '6206', '6207', '6208'
    ]);
    
    const hotSearches = reactive([
      '深沟球轴承', '角接触球轴承', '圆锥滚子轴承', 
      '调心球轴承', '推力球轴承', '圆柱滚子轴承'
    ]);
    
    const searchResults = reactive([]);

    const goBack = () => {
      Taro.navigateBack();
    };

    const handleInput = (e) => {
      searchText.value = e.detail.value;
      hasSearched.value = false;
    };

    const handleSearch = () => {
      if (!searchText.value.trim()) return;
      
      hasSearched.value = true;
      
      // 添加到搜索历史
      if (!searchHistory.includes(searchText.value)) {
        searchHistory.unshift(searchText.value);
        if (searchHistory.length > 10) {
          searchHistory.pop();
        }
      }
      
      // 模拟搜索结果
      searchResults.splice(0, searchResults.length);
      if (searchText.value.includes('6205')) {
        searchResults.push(
          { title: '6205深沟球轴承', description: '高品质进口轴承', price: '25.00' },
          { title: '6205-2RS密封轴承', description: '双面密封设计', price: '35.00' }
        );
      }
      
      Taro.showToast({
        title: `搜索: ${searchText.value}`,
        icon: 'none'
      });
    };

    const selectHistory = (item) => {
      searchText.value = item;
      handleSearch();
    };

    const selectHotSearch = (item) => {
      searchText.value = item;
      handleSearch();
    };

    const clearHistory = () => {
      searchHistory.splice(0, searchHistory.length);
      Taro.showToast({
        title: '已清空搜索历史',
        icon: 'none'
      });
    };

    return {
      searchText,
      hasSearched,
      searchHistory,
      hotSearches,
      searchResults,
      goBack,
      handleInput,
      handleSearch,
      selectHistory,
      selectHotSearch,
      clearHistory
    };
  }
};
</script>

<style>
.search-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-header {
  background-color: white;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.back-btn {
  font-size: 36rpx;
  color: #333;
  padding: 10rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 50rpx;
  padding: 0 30rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx 0;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.search-btn {
  color: #007bff;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.search-history, .hot-search {
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.history-title, .hot-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.clear-history {
  color: #6c757d;
  font-size: 24rpx;
}

.history-tags, .hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.history-tag, .hot-tag {
  background-color: #f8f9fa;
  color: #6c757d;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.search-results {
  margin: 20rpx;
}

.result-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.result-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 15rpx;
}

.result-price {
  font-size: 32rpx;
  color: #dc3545;
  font-weight: bold;
}

.no-results {
  text-align: center;
  padding: 100rpx 30rpx;
  color: #6c757d;
}

.no-results-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.no-results-text {
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.no-results-tip {
  font-size: 24rpx;
}
</style>
