<template>
  <web-view id="web" :src="url" :progressbarColor="progressbarColor" @message="handleMessage" />
</template>

<script>
  import web from '../../../src/components/webview/webview'
  const page = web.create("$--code-");
  export default {
    onLoad: page.onLoad,
    onShow: page.onShow,
    onAddToFavorites: page.onAddToFavorites,
    onShareAppMessage: page.onShareAppMessage,
    onShareTimeline: page.onShareTimeline,
    methods: page.methods,
    data: page.data,
    created() {
      page.setVue(this);
    }
  }
</script>
