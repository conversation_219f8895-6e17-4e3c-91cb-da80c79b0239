<template>
  <view>{{ aa }}</view>
</template>

<script>
import Taro from "@tarojs/taro";
export default {
  onLoad(opt) {
    console.log(opt);
    const ensureParameter = p => {
      if (process.env.TARO_ENV != "alipay") {
        //这些平台得到的是原始参数，没有解码
        if (p) {
          for (let key in p) {
            p[key] = decodeURIComponent(p[key]);
          }
        }
      }
      return p;
    };
    const p = ensureParameter(opt);
    console.log(p);
    Taro.navigateToMiniProgram({
      appId: "wx2c348cf579062e56",
      path: "/packages/restaurant/restaurant/restaurant?poi_id=882337906062141",
      success: function(res) {
        // 打开成功
      }
    });
    // setTimeout(() => {
    //   Taro.navigateBack();
    // }, 3000);
  },
  setup(opt) {
    console.log(opt);
    return { aa: 123 };
  }
};
</script>
