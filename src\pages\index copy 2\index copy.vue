<template>
  <view class="main">
    <image class="logo" src="https://i.cdn.yimenapp.com/ag/35/200.png"></image>
    <view class="title-name">
      <text>第110届全国糖酒商品交易会</text>
    </view>
    <view class="title-des">
      <text>THE 110 CHINA FOOD & DRINKS FAIR</text>
    </view>
    <view class="addr-name">
      <text>祥宇宾馆</text>
    </view>
    <view class="wel-name">
      <text>— 欢 迎 你 —</text>
    </view>
    <view class="sign-tip" v-if="false">
      <text>您已签到成功！</text>
    </view>
    <view class="plate-wrap-box">
      <view class="plate-border" :animation="data.aniData">
        <image class="iconWheelCircle" :src="iconWheelCircle" />
        <view class="plate-wrap">
          <view
            v-for="(item, index) in data.lottery"
            class="plate-box"
            key
            :style="
              'top:-' +
                (data.lottery.length - 6 <= 2
                  ? 36 + 4 * (data.lottery.length - 6)
                  : 50) +
                'rpx;transform-origin: 50% ' +
                (data.lottery.length - 6 <= 2
                  ? 256 + 4 * (data.lottery.length - 6)
                  : 270) +
                'rpx;border-top: ' +
                (data.lottery.length - 6 <= 2
                  ? 256 + 4 * (data.lottery.length - 6)
                  : 270) +
                'rpx solid #' +
                (index % 2 == 0 ? 'ffffff' : 'ff4f5f') +
                ';transform:translate(-50%,0) rotate(' +
                (360 / data.lottery.length) * index +
                'deg);border-left: ' +
                (440 / data.lottery.length) * 2 +
                'rpx solid transparent;border-right: ' +
                (440 / data.lottery.length) * 2 +
                'rpx solid transparent;'
            "
          >
            <text
              class="text-box"
              :style="'color:#' + (index % 2 == 0 ? 'ff4f5f' : 'ffffff')"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>

      <view class="plate-btn-wrap" @tap="startRollTap">
        <image class="iconWheelStart" :src="iconWheelStart" />
      </view>
    </view>

    <view class="wheel-modal" v-if="data.visibleModal">
      <view class="wheel-modal-mask"></view>
      <view class="wheel-modal-content">
        <image
          class="modal-img"
          :src="data.isWin ? iconWheelModalSuccess : iconWheelModalFail"
        />
        <view class="modal-text">
          <text>{{
            data.isWin ? `您获得${data.prizeName}` : "很遗憾您没有中奖"
          }}</text>
        </view>
        <view class="modal-btnGroup" v-if="data.isWin">
          <view class="modal-btn-giveUp" @tap="closeModal">
            <text>放弃奖品</text>
          </view>
          <view
            :class="[
              data.prizeType == 0 || data.prizeType == 1
                ? 'modal-btn-get-short'
                : '',
              data.prizeType == 2 ? 'modal-btn-get-long' : ''
            ]"
            @tap="closeModal"
          >
            <text>
              {{ data.prizeType == 2 ? "联系客服领取奖品" : "立即领取" }}
            </text>
          </view>
        </view>
        <view class="modal-btnGroup" v-else>
          <view class="modal-btn-continue" @tap="closeModal">
            <text>继续努力</text>
          </view>
        </view>
      </view>
    </view>
    <view class="history-modal" v-if="data.visibleHistoryModal">
      <view class="history-modal-mask"></view>
      <view class="history-modal-content">
        <image
          @tap="showHistoryModal(false)"
          class="iconWheelModalClose"
          :src="iconWheelModalClose"
        />

        <view class="history-modal-header">
          <text>中奖历史</text>
        </view>
        <view :style="{ display: 'flex' }">
          <view class="history-modal-title">
            <text>日期</text>
          </view>
          <view class="history-modal-title">
            <text>奖品名称</text>
          </view>
          <view class="history-modal-title">
            <text>奖品</text>
          </view>
          <view class="history-modal-title">
            <text>兑换情况</text>
          </view>
        </view>
        <view class="history-modal-table">
          <view v-for="item in data.historyData" :style="{ display: 'flex' }">
            <view class="history-modal-table-item">
              <text>{{ item.winningTime }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.awardName }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.award }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.exchangeStatusName }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="menu">
      <view class="menu-item"><text>我的礼品</text></view>
      <view class="menu-item cccc"><text>咨询电话</text> </view>
      <view class="menu-item eeee">
        <view>
          <text>祥宇宾馆</text>
        </view>
        <text>VR数字展厅</text>
      </view>
    </view>
  </view>
  <view class="container" v-show="false">
    <image class="iconWheelBg" :src="iconWheelBg" />
    <view class="history" @tap="showHistoryModal(true)">
      <text>中奖历史</text>
    </view>
    <view class="title">
      <text>{{ data.title }}</text>
    </view>
    <view class="rollNum">
      <text>您共有{{ `${data.canRollNum}` }}次抽奖机会</text>
    </view>
    <view class="plate-wrap-box">
      <view class="plate-border" :animation="data.aniData">
        <image class="iconWheelCircle" :src="iconWheelCircle" />
        <view class="plate-wrap">
          <view
            v-for="(item, index) in data.lottery"
            class="plate-box"
            key
            :style="
              'top:-' +
                (data.lottery.length - 6 <= 2
                  ? 36 + 4 * (data.lottery.length - 6)
                  : 50) +
                'rpx;transform-origin: 50% ' +
                (data.lottery.length - 6 <= 2
                  ? 256 + 4 * (data.lottery.length - 6)
                  : 270) +
                'rpx;border-top: ' +
                (data.lottery.length - 6 <= 2
                  ? 256 + 4 * (data.lottery.length - 6)
                  : 270) +
                'rpx solid #' +
                (index % 2 == 0 ? 'ffffff' : 'ff4f5f') +
                ';transform:translate(-50%,0) rotate(' +
                (360 / data.lottery.length) * index +
                'deg);border-left: ' +
                (440 / data.lottery.length) * 2 +
                'rpx solid transparent;border-right: ' +
                (440 / data.lottery.length) * 2 +
                'rpx solid transparent;'
            "
          >
            <text
              class="text-box"
              :style="'color:#' + (index % 2 == 0 ? 'ff4f5f' : 'ffffff')"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>

      <view class="plate-btn-wrap" @tap="startRollTap">
        <image class="iconWheelStart" :src="iconWheelStart" />
      </view>

      <view class="detial-wrap">
        <image class="iconWheelTitle" :src="iconWheelTitle" />
        <view class="detail-text">
          <text>活动详情</text>
        </view>
        <image class="iconWheelRuleBg" :src="iconWheelRuleBg" />
        <view class="rule-detail">
          <text>
            一、这里就是签到规则这里就是签到规则这里就是签到规则
            二、这里就是签到规则这里就是签到规则这里就是签到规则这里就是签到规则这里就是签到规则这里就是签到规则
            三、这里就是签到规则这里就是签到规则这里就是签到规则这里就是签到规则这里就是签到规则
          </text>
        </view>
      </view>
    </view>

    <view class="wheel-modal" v-if="data.visibleModal">
      <view class="wheel-modal-mask"></view>
      <view class="wheel-modal-content">
        <image
          class="modal-img"
          :src="data.isWin ? iconWheelModalSuccess : iconWheelModalFail"
        />
        <view class="modal-text">
          <text>{{
            data.isWin ? `您获得${data.prizeName}` : "很遗憾您没有中奖"
          }}</text>
        </view>
        <view class="modal-btnGroup" v-if="data.isWin">
          <view class="modal-btn-giveUp" @tap="closeModal">
            <text>放弃奖品</text>
          </view>
          <view
            :class="[
              data.prizeType == 0 || data.prizeType == 1
                ? 'modal-btn-get-short'
                : '',
              data.prizeType == 2 ? 'modal-btn-get-long' : ''
            ]"
            @tap="closeModal"
          >
            <text>
              {{ data.prizeType == 2 ? "联系客服领取奖品" : "立即领取" }}
            </text>
          </view>
        </view>
        <view class="modal-btnGroup" v-else>
          <view class="modal-btn-continue" @tap="closeModal">
            <text>继续努力</text>
          </view>
        </view>
      </view>
    </view>
    <view class="history-modal" v-if="data.visibleHistoryModal">
      <view class="history-modal-mask"></view>
      <view class="history-modal-content">
        <image
          @tap="showHistoryModal(false)"
          class="iconWheelModalClose"
          :src="iconWheelModalClose"
        />

        <view class="history-modal-header">
          <text>中奖历史</text>
        </view>
        <view :style="{ display: 'flex' }">
          <view class="history-modal-title">
            <text>日期</text>
          </view>
          <view class="history-modal-title">
            <text>奖品名称</text>
          </view>
          <view class="history-modal-title">
            <text>奖品</text>
          </view>
          <view class="history-modal-title">
            <text>兑换情况</text>
          </view>
        </view>
        <view class="history-modal-table">
          <view v-for="item in data.historyData" :style="{ display: 'flex' }">
            <view class="history-modal-table-item">
              <text>{{ item.winningTime }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.awardName }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.award }}</text>
            </view>
            <view class="history-modal-table-item">
              <text>{{ item.exchangeStatusName }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const baseUrl = "http://***************/";
    const iconWheelBg = baseUrl + "static/<EMAIL>";
    const iconWheelCircle = baseUrl + "static/<EMAIL>";
    const iconWheelModalFail = baseUrl + "static/<EMAIL>";
    const iconWheelModalSuccess = baseUrl + "static/<EMAIL>";
    const iconWheelRuleBg = baseUrl + "static/<EMAIL>";
    const iconWheelStart = baseUrl + "static/<EMAIL>";
    const iconWheelTitle = baseUrl + "static/<EMAIL>";
    const iconWheelModalClose = baseUrl + "static/icon-close.png";
    // type 页面类型  init 需要获取code 和手机号  如果是  sign  就进入自动签到场景
    const data = reactive({
      activeid: 1,
      type: "init",
      token: "",
      aniData: null,
      rollState: false, // 是否正在抽奖
      visibleModal: false, //抽奖弹窗
      visibleHistoryModal: false, //中奖历史弹窗
      canRollNum: 3, // 抽奖次数
      prizeType: 0, // 奖品类型 0-优惠券1-积分2-实物
      prizeName: null,
      isWin: false, //是否中奖
      num: 1, //用在动画上，让用户在第二次点击的时候可以接着上次转动的角度继续转
      lottery: [
        "一等奖",
        "二等奖",
        "谢谢参与",
        "三等奖",
        "二等奖",
        "谢谢参与",
        "三等奖",
        "谢谢参与"
      ], //奖品数组
      title: "抽奖页面标题",
      lotteryArrLen: 8, //放奖品的数组的长度
      historyData: [
        {
          award: "华为xxxxxxxxxxxxxx",
          awardName: "一等奖",
          exchangeStatusName: "已领取",
          winningTime: "2019-10-16"
        }
      ]
    });
    const handleGetUserProfile = () => {
      Taro.getUserProfile({
        lang: "zh_CN",
        desc: "获取你的昵称、头像、地区及性别",
        success: response => {
          console.log(response);
          const wxUserInfo = response.userInfo;
          // const { openId } = this.state;
          console.log("getUserProfile", wxUserInfo);
          Taro.setStorageSync("UserInfo", {
            wxUserInfo: wxUserInfo,
            name: 123
          });
          Taro.getStorage({
            key: "UserInfo",
            success(res) {
              console.log(" 我是缓存数据", res);
            }
          });
        },
        fail: () => {
          //拒绝授权
          console.error("您拒绝了请求");
          return;
        }
      });
    };
    const getTel = e => {
      console.log(e.detail);
      let { encryptedData, iv } = e.detail;
      Taro.login({
        success(res) {
          console.log(res);
          let code = res.code;
          Taro.request({
            url: "url",
            method: "GET",
            data: {
              code,
              encryptedData,
              iv
            },
            success(phoneNumber) {
              console.log(phoneNumber);
            }
          });
        }
      });
    };
    onMounted(() => {
      data.aniData = Taro.createAnimation({
        //创建动画对象
        duration: 3000,
        timingFunction: "ease"
      });

      // 获取定位信息
      Taro.getLocation({
        type: "gcj02",
        success: function(res) {
          console.log(res);
        }
      });
      // 从服务端获取信息
      if (data.token == "") {
        return;
      }

      Taro.request({
        url: "",
        method: "POST",
        data: {},
        success(res) {
          console.log(res);
        }
      });
      Taro.getStorage({
        key: "token",
        success: function(res) {
          if (res.data) {
            data.token = res.data;
            data.type = "auto_sign";
          }
        }
      });
    });
    const showModal = () => {
      console.log("抽奖结束---显示弹窗");
    };
    const closeModal = () => {
      data.visibleModal = false;
    };
    const showHistoryModal = flag => {
      data.visibleHistoryModal = flag;
    };
    const startRollTap = () => {
      //开始转盘
      const {
        lotteryArrLen,
        lottery,
        canRollNum,
        rollState,
        visibleModal,
        num,
        isWin,
        aniData
      } = data;

      if (canRollNum < 1 || rollState || visibleModal) return;

      let rightNum = ~~(Math.random() * lotteryArrLen); //生成随机数
      console.log("抽奖次数--", canRollNum);
      console.log(`随机数是${rightNum}`);
      // 随机次数为2,5,7则未中奖
      if (rightNum == 2 || rightNum == 5 || rightNum == 7) {
        data.isWin = false;
      } else {
        data.isWin = true;
        data.prizeName = lottery[rightNum];
      }
      console.log(`奖品是：${lottery[rightNum]}`);
      data.aniData.rotate(3600 * num - (360 / lotteryArrLen) * rightNum).step(); //设置转动的圈数
      data.rollState = true;
      data.aniData = data.aniData.export(); //导出动画队列。export 方法每次调用后会清掉之前的动画操作。
      // 动画停止设置状态
      setTimeout(_ => {
        showModal();
        data.visibleModal = true;
        data.canRollNum = canRollNum - 1; // 抽奖次数减一
        data.rollState = false; // 将转盘状态切换为可抽奖
        data.num = num + 1;
        data.aniData = Taro.createAnimation({
          //创建动画对象
          duration: 3000,
          timingFunction: "ease"
        });
      }, 3000);
    };
    return {
      iconWheelBg,
      iconWheelCircle,
      iconWheelModalFail,
      iconWheelModalSuccess,
      iconWheelRuleBg,
      iconWheelStart,
      iconWheelTitle,
      iconWheelModalClose,
      data,
      handleGetUserProfile,
      getTel,
      showModal,
      closeModal,
      showHistoryModal,
      startRollTap
    };
  }
};
</script>

<style>
.main {
  height: 100vh;
  width: 100vw;
  background-image: url("http://***************/static/bg.jpg");
  background-repeat: no-repeat;
  background-size: cover;
}
.logo {
  margin: 5vw 0 0 5vw;
  width: 20vw;
  height: 20vw;
}

.title-name {
  color: #fff;
  text-align: center;
  font-weight: 700;
  font-size: 45px;
}
.title-des {
  color: #fff;
  text-align: center;
  font-size: 30px;
}
.addr-name {
  color: #fff;
  text-align: center;
  margin-top: 50px;
  font-weight: 700;
  font-size: 40px;
  background: linear-gradient(to right, #ffffff00, #00a2ea, #00a2ea, #ffffff00);
  width: 66vw;
  margin-left: 17vw;
  letter-spacing: 10px;
}
.wel-name {
  color: #fff;
  text-align: center;
  font-size: 35px;
  margin-top: 10px;
}
.sign-tip {
  color: #fff;
  text-align: center;
  font-weight: 700;
  margin-top: 200px;
  font-size: 50px;
}
.menu {
  position: fixed;
  bottom: 0;
  height: 80px;
  width: 100vw;
  line-height: 80px;
  background: #e6e6e6;
  display: flex;
}
.menu .menu-item {
  flex: 1;
  text-align: center;
  font-size: 28px;
  color: #4d4d4d;
}
.eeee {
  line-height: 40px;
}
.cccc {
  border-left: 1px solid #b9b9b9;
  border-right: 1px solid #b9b9b9;
}
page {
  width: 100%;
  height: 100%;
}

.container {
  z-index: 1;
  height: 100%;
}
.iconWheelBg {
  width: 750px;
  height: 1750px;
  z-index: -1;
  position: absolute;
}
.history {
  width: 750px;
  height: 35px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  text-decoration: underline;
  color: rgba(255, 255, 255, 1);
  line-height: 35px;
  text-decoration: underline;
  position: absolute;
  text-align: right;
  top: 30px;
  right: 20px;
}
.title {
  width: 750px;
  height: 80px;
  font-size: 70px;
  font-family: PingFang SC;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  line-height: 80px;
  text-align: center;
  letter-spacing: 8px;
  position: absolute;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  top: 95px;
}
.rollNum {
  width: 750px;
  height: 30px;
  font-size: 29px;
  font-family: PingFang SC;
  font-weight: 100;
  color: rgba(255, 255, 255, 1);
  line-height: 30px;
  text-align: center;
  letter-spacing: 4px;
  position: absolute;
  top: 190px;
}
.plate-wrap-box {
  position: relative;
  z-index: 999;
  width: 750px;
  height: 678px;
  border-radius: 50%;
  top: 325px;
  text-align: center;
  display: flex;
  justify-content: center;
}
.iconWheelCircle {
  width: 678px;
  height: 678px;
  z-index: 9;
  position: relative;
  border-radius: 50%;
}
.plate-border {
  width: 678px;
  height: 678px;
  position: relative;
  display: flex;
  justify-content: center;
}

.plate-wrap {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
  width: 440px;
  height: 440px;
  border: 5px solid #f8ea6e;
  border-radius: 50%;
  overflow: hidden;
  margin: auto;
}

.plate-light {
  z-index: 1;
  width: 500px;
  height: 500px;
  border: none;
}

.plate-box {
  position: absolute;
  z-index: 1;
  left: 50%;
  width: 0;
  height: 0;
  font-size: 24px;
}

.bulb {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 50%;
  top: -242px;
  transform: translate(-50%, 0);
  border-radius: 50%;
  background-color: #fff0c7;
  filter: blur(0.5px);
}

.text-box {
  position: absolute;
  text-align: center;
  display: inline-block;
  width: 140px;
  word-break: break-all;
  top: -200px;
  transform: translate(-100%, 0);
  font-size: 19px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  left: 70px;
}

.plate-btn-wrap {
  position: absolute;
  z-index: 10;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 126px;
  height: 149px;
  border-radius: 50%;
}
.iconWheelStart {
  width: 126px;
  height: 149px;
  position: relative;
  z-index: 11;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.detial-wrap {
  position: absolute;
  z-index: 10;
  left: 50%;
  top: 950px;
  transform: translate(-50%, -50%);
  width: 750px;
  height: 630px;
  border-radius: 50%;
}
.iconWheelTitle {
  width: 400px;
  height: 110px;
  top: 75px;
  position: relative;
}
.detail-text {
  width: 250px;
  height: 95px;
  font-size: 45px;
  font-family: Source Han Sans CN;
  font-weight: 800;
  color: #a13d01;
  line-height: 80px;
  position: absolute;
  top: 80px;
  left: 255px;
  text-align: center;
  letter-spacing: 5px;
}
.iconWheelRuleBg {
  width: 600px;
  height: 600px;
}
.rule-detail {
  width: 520px;
  height: 480px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 550;
  color: #f52c32;
  overflow: hidden;
  position: absolute;
  top: 200px;
  left: 120px;
  text-align: start;
}

.wheel-modal {
  width: 100vw;
  height: 85vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
.wheel-modal-mask {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.wheel-modal-content {
  width: 750px;
  height: 450px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.modal-img {
  width: 705px;
  height: 520px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.modal-text {
  height: 60px;
  line-height: 60px;
  width: 100%;
  text-align: center;
  position: absolute;
  top: 265px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
}
.modal-btnGroup {
  width: 750px;
  height: 80px;
  display: flex;
  justify-content: center;
  position: absolute;
  top: 355px;
}
.modal-btn-giveUp {
  width: 224px;
  height: 80px;
  background: rgba(255, 255, 255, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  margin-right: 10px;
}
.modal-btn-get-short {
  width: 224px;
  height: 80px;
  background: rgba(248, 228, 55, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
  margin-left: 10px;
}
.modal-btn-get-long {
  width: 290px;
  height: 80px;
  background: rgba(248, 228, 55, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
  margin-left: 10px;
}
.modal-btn-continue {
  width: 224px;
  height: 80px;
  background: rgba(255, 255, 255, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
}

.history-modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
.history-modal-mask {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.history-modal-content {
  width: 635px;
  height: 500px;
  position: absolute;
  background: rgba(255, 255, 255, 1);
  border-radius: 20px;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.iconWheelModalClose {
  width: 64px;
  height: 64px;
  position: absolute;
  top: -100px;
  right: 0;
}
.history-modal-header {
  width: 635px;
  height: 100px;
  background: rgba(255, 79, 95, 1);
  border-radius: 20px 20px 0px 0px;
  text-align: center;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 100px;
}
.history-modal-title {
  height: 70px;
  width: 25%;
  line-height: 70px;
  text-align: center;
  font-size: 24px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(102, 102, 102, 1);
}
.history-modal-table {
  width: 635px;
  height: 325px;
  overflow: auto;
  margin-bottom: 1px;
}
.history-modal-table-item {
  height: 55px;
  width: 25%;
  line-height: 55px;
  text-align: center;
  font-size: 24px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(102, 102, 102, 1);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
