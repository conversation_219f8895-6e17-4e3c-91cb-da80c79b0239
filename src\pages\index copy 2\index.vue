<template>
  <view class="header-search">
    <view class="header-image" @tap="open(data.info.center)">
      <image
        src="https://www.fj139.com/api/avatar/show.php?username=&size=middle"
      />
    </view>
    <view class="search-box" @tap="open(data.info.search)">
      <icon size="16" type="search" />
      <text>请输入关键字搜索</text>
    </view>
    <view class="header-image h22" @tap="call(data.info.phone)">
      <image src="https://www.fj139.com/mobile/static/img/icon-service.png" />
    </view>
  </view>
  <swiper
    class="banner"
    indicator-color="#999"
    indicator-active-color="#007aff"
    :vertical="false"
    :circular="true"
    :indicator-dots="true"
    :autoplay="true"
  >
    <swiper-item v-for="item in data.info.banner" @tap="open(item.url)">
      <image class="banner-img" :src="item.img" />
    </swiper-item>
  </swiper>
  <view class="menu">
    <view
      class="menu-item"
      v-for="item in data.info.items"
      :key="item"
      @tap="open(item.url)"
    >
      <view class="menu-icon">
        <image :src="item.img" />
      </view>
      <view class="menu-text">
        <text>{{ item.title }}</text>
      </view>
    </view>
  </view>
  <view class="info-boxs">
    <swiper
      class="info-box"
      indicator-color="#999"
      indicator-active-color="#007aff"
      :vertical="false"
      :circular="true"
      :indicator-dots="true"
      :autoplay="true"
    >
      <swiper-item v-for="item in data.info.ad1" @tap="open(item.url)">
        <image class="info-img" :src="item.img" />
      </swiper-item>
    </swiper>
    <swiper
      class="info-box"
      indicator-color="#999"
      indicator-active-color="#007aff"
      :vertical="false"
      :circular="true"
      :indicator-dots="true"
      :autoplay="true"
    >
      <swiper-item v-for="item in data.info.ad2" @tap="open(item.url)">
        <image class="info-img" :src="item.img" />
      </swiper-item>
    </swiper>
  </view>
  <view class="t-lists" v-for="item in data.info.t">
    <view class="t-header">
      <view class="t-title">
        <text>{{ item.title }}</text>
      </view>
      <view class="t-b"></view>
      <view class="t-more" @tap="open(item.url)">
        <text>
          更多
        </text>
        <image src="https://www.fj139.com/mobile/static/img/list-set.png" />
      </view>
    </view>
    <view class="t-item" v-for="tt in item.list" @tap="open(tt.url)">
      <view class="t-item-img" v-if="tt.img">
        <image :src="tt.img" />
      </view>
      <view class="t-item-info">
        <view class="t-item-title">
          <text>{{ tt.title }}</text>
        </view>
        <view class="t-item-b">
          <view class="time">
            <image
              src="https://www.fj139.com/mobile/static/img/ico-time.png"
            ></image>
            <text>{{ tt.time }}</text>
          </view>
          <view class="pl">
            <image
              src="https://www.fj139.com/mobile/static/img/ico-comm.png"
            ></image>
            <text>{{ tt.comments }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const data = reactive({
      info: {
        center: "",
        search: "",
        phone: "",
        banner: [],
        items: [],
        ad1: [],
        ad2: [],
        t: []
      }
    });
    const open = url => {
      Taro.navigateTo({
        url: "/pages/test/index?url=" + encodeURIComponent(url)
      });
    };
    const call = num => {
      Taro.makePhoneCall({
        phoneNumber: num
      });
    };

    onMounted(() => {
      Taro.request({
        url: "https://www.fj139.com/mobile/api.php",
        header: {
          "content-type": "application/json" // 默认值
        },
        success: function(res) {
          if (res.statusCode == 200) {
            data.info = res.data;
          }
        }
      });
    });
    return { data, open, call };
  }
};
</script>

<style>
#app {
  background: #f6f6f6;
}
.banner {
  width: 100vw;
  height: 30.3vw;
}
.banner-img {
  width: 100vw;
  height: 30.3vw;
}
.header-search {
  height: 90px;
  display: flex;
  text-align: left;
  background: #f7f7f7;
}

.header-image {
  width: 90px;
  height: 90px;
}

.header-image image {
  object-fit: cover;
  width: 62px;
  height: 62px;
  margin: 14px 20px;
  border-radius: 50%;
}
.h22 image {
  width: 48px;
  height: 48px;
  margin: 21px 20px;
}

.header-search .search-box {
  flex: 1;
  margin: 10px;
  line-height: 70px;
  font-size: 26px;
  color: #8b8b8b;
  background: #eee;
  border-radius: 10px;
  padding-left: 20px;
}

.header-search .search-box text {
  padding-left: 30px;
}
.menu {
  margin-top: 20px;
  display: flex;
  background: #fff;
  flex-wrap: wrap;
}

.menu-item {
  margin: 0 2vw;
  width: 16vw;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.menu-icon image {
  width: 12vw;
  height: 12vw;
  margin: 2vw 0;
}

.menu-text {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 10px;
  overflow: hidden;
  white-space: nowrap;
}
.info-boxs {
  display: flex;
  justify-content: space-around;
  background: #f6f6f6;
}
.info-box {
  width: 49vw;
  height: 25.65vw;
}
.info-img {
  width: 49vw;
  height: 25.65vw;
}
.t-lists {
  background: #f6f6f6;
}
.t-header {
  display: flex;
  height: 88px;
  line-height: 88px;
  margin-top: 20px;
  position: relative;
}
.t-title {
  flex: 1;
  font-weight: 700;
  font-size: 36px;
  padding-left: 40px;
}
.t-more {
  font-size: 30px;
  display: flex;
  color: #666666;
}
.t-b {
  position: absolute;
  height: 8px;
  width: 68px;
  bottom: 0;
  left: 40px;
  background: #ff6600;
  border-radius: 4px;
}
.t-more image {
  padding-left: 5px;
  width: 57.5px;
  height: 32.5px;
  margin-top: 27.75px;
}
.t-item {
  background: #fff;
  margin-bottom: 20px;
  padding: 40px;
  display: flex;
}
.t-item-img {
  width: 200px;
  height: 200px;
  padding-right: 20px;
}
.t-item-img image {
  width: 200px;
  height: 200px;
}
.t-item-info {
  flex: 1;
}
.t-item-title {
  font-size: 32px;
  font-weight: 600;
  color: #444444;
}
.t-item-b {
  display: flex;
  font-size: 30px;
  color: #999999;
  height: 40px;
  line-height: 40px;
  padding-top: 20px;
}
.time {
  flex: 1;
  display: flex;
}
.time image {
  padding: 6px;
  width: 28px;
  height: 28px;
}
.pl {
  display: flex;
}
.pl image {
  padding: 6px;
  width: 28px;
  height: 28px;
}
</style>
