<template>
  
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const data = reactive({
      info:{
        title:"烟店轴承市场",
        serchUrl:"",
        banners:[
          {
            img:"https://shop.cdn.yimenapp.com/images/3774/2024/07/ka66ZhDzHWxU95U9Mwu1zH5hJRMtat.jpg?x-oss-process=style/q80",
            url:"https://www.001zcw.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=merch&merchid=323",
          }
        ],
        log:"轴承经销商采购进货必备平台！",
        tabs:[
          {
            icon:"https://shop.cdn.yimenapp.com/images/3774/2024/02/WjNVw7whw0OVb92tVOKVhBWwToIJb0.png?x-oss-process=style/p",
            title:"市场商家",
            url:"https://www.001zcw.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=merch&merchid=323",
          },
          {
            icon:"https://shop.cdn.yimenapp.com/images/3774/2024/12/bZQQ97DDuChimi399zhjg9hdp9QgC3.jpg?x-oss-process=style/q80",
            title:"三大厂",
            url:"https://shop.yimenapp.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=diypage&id=2425",
          }
        ],
        ad:{
          img:"https://shop.cdn.yimenapp.com/images/3774/2024/09/kDvfEfUWFj6Ud53d6efHeYW6y695nC.jpg?x-oss-process=style/q80",
          url:"https://www.001zcw.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=merch&merchid=580"
        },
        footer:"烟店轴承市场 版权所有",
        menus:[
          {
             icon:"https://shop.cdn.yimenapp.com/images/3774/2024/12/bZQQ97DDuChimi399zhjg9hdp9QgC3.jpg?x-oss-process=style/q80",
            title:"首页",
            url:"https://shop.yimenapp.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=diypage&id=2425",
          },
          {
             icon:"https://shop.cdn.yimenapp.com/images/3774/2024/12/bZQQ97DDuChimi399zhjg9hdp9QgC3.jpg?x-oss-process=style/q80",
            title:"全部分类",
            url:"https://shop.yimenapp.com/app/index.php?i=3774&c=entry&m=ewei_shopv2&do=mobile&r=diypage&id=2425",
          }
        ]
      }
    });
    const open = url => {
      Taro.navigateTo({
        url: "/pages/test/index?url=" + encodeURIComponent(url)
      });
    };
    const call = num => {
      Taro.makePhoneCall({
        phoneNumber: num
      });
    };

    onMounted(() => {
      Taro.request({
        url: "https://shop.yimenapp.com/app/3774.json",
        header: {
          "content-type": "application/json" // 默认值
        },
        success: function(res) {
          if (res.statusCode == 200) {
            data.info = res.data;
          }
        }
      });
    });
    return { data, open, call };
  }
};
</script>

<style>

</style>
