<!--{subtemplate jameson_read:touch/header}-->
<div class="header back-0">
	<div class="weui-flex">
	  <div class="weui-flex__item header_left">
      <!--{if $_G['jameson_read']['is_author']}-->
      <a href="{eval echo mbreurl('author',0);}">
        <i class="fa fa-fw fa-address-book-o"></i>
      </a>
      {else}
      <a {if !$_G['uid']} href="/member.php?mod=logging&amp;action=login" {else} href="{eval echo mbreurl('shenqing');}"{/if}>
        <i class="fa fa-fw fa-plus"></i>
      </a>      
      <!--{/if}-->
    </div>
    <div class="weui-flex__item header_title" onclick="location.href='{eval echo mbreurl("search",0);}'">
      <div class="weui-search-bar">
      <form class="weui-search-bar__form" action="./plugin.php">
        <div class="weui-search-bar__box">
          <input type="search" class="weui-search-bar__input" id="searchInput"   required="" name="keyword">
          <input type="hidden" name="id" value="jameson_read">
          <input type="hidden" name="contrl" value="touch">
          <input type="hidden" name="act" value="search">
          <a href="javascript:" class="weui-icon-clear" id="searchClear"></a>
        </div>
        <label class="weui-search-bar__label" id="searchText">
          <i class="weui-icon-search"></i>
          <span>{lang jameson_read:sousuo}</span>
        </label>
      </form>
      </div>
    </div>
    <div class="weui-flex__item header_right flex-1">
      <a href="javascript:;" class="open-popup" data-target="#bottomtab">
        <i class="fa fa-fw fa-bars"></i>
      </a>
    </div>
	</div>
</div>
<!--幻灯-->
<!--{if $flash}-->
<div class="swiper-container weui-panel">
  <div class="swiper-wrapper">
	<!--{loop  $flash $img}-->
	<!--{if $img['image']}-->
    <div class="swiper-slide">
    	<a href="{$img['url']}"><img src="{$img['image']}" alt="古籍電子書在線閱讀"></a>
    </div>
    <!--{/if}-->
    <!--{/if}-->
  </div>
  <div class="swiper-pagination"></div>
</div>
<!--{/if}-->
<!-- 4个图标 -->
<div class="weui-grids weui-panel">
  <a href="{eval echo mbreurl('store',0);}" class="weui-grid five_one">
    <div class="text-danger">
      <i class="fa fa-fw fa-university "></i>
    </div>
    <p class="weui-grid__label">
      <!--{lang jameson_read:shujia}-->
    </p>
  </a>  
  <a href="{eval echo mbreurl('more',0,'addtime');}" class="weui-grid five_one">
    <div class="text-danger">
      <i class="fa fa-fw fa-ticket "></i>
    </div>
    <p class="weui-grid__label">
      <!--{lang jameson_read:gengxin}-->
    </p>
  </a>
  <a href="{eval echo mbreurl('fenlei');}" class="weui-grid five_one">
    <div class="text-danger">
      <i class="fa fa-fw fa-list "></i>
    </div>
    <p class="weui-grid__label">
      <!--{lang jameson_read:fenlei}-->
    </p>
  </a>
  <a href="{eval echo mbreurl('paihang');}" class="weui-grid five_one">
    <div class="text-danger">
      <i class="fa fa-fw fa-sort-amount-asc "></i>
    </div>
    <p class="weui-grid__label">
      <!--{lang jameson_read:paihang}-->
    </p>
  </a>
  <a href="{eval echo mbreurl('more',0,'end');}" class="weui-grid five_one">
    <div class="text-danger">
      <i class="fa fa-fw fa-hourglass-end "></i>
    </div>
    <p class="weui-grid__label">
      <!--{lang jameson_read:yiwanjie}-->
    </p>
  </a>
</div>
<!--{if $indexadv[0]['adv']}-->
<div class="weui-panel weui-media-box zdyadv">
  <div class="weui-panel__bd">
    <!--{eval echo stripcslashes($indexadv[0]['adv']);}-->
  </div>
</div>
<!--{/if}-->
<!-- 最近阅读 -->
{if $historys}
<div class="rementushu">
  <div class="h_title mb10 mt10">
  <span class="text-danger">|</span>
  {lang jameson_read:liulanlishi}</div>
  <div class="overflow-auto mt10">
    {loop $historys $book}
      <a style="height: auto" href="{eval echo mbreurl('thread',$book['tid'],$book['pid'],$_G['uid']);}">
        <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
        <p><span>{$book['book_name']}</span><span>[{$book['subject']}]</span></p>
      </a>
    {/loop}
  </div>
</div>
{/if}
<!-- 热门图书 -->
<div class="rementushu">
  <div class="h_title mb10 mt10">
  <span class="text-danger">|</span>
  {lang jameson_read:remen}{lang jameson_read:tushu}</div>
  <div class="overflow-auto mt10">
    {loop $paihang['haoping'] $book}
      <a href="{eval echo mbreurl('book',$book['book_id']);}">
        <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
        <span>{$book['book_name']}</span>
      </a>
    {/loop}
  </div>
</div>





<!--{if $indexadv[1]['adv']}-->
<div class="weui-panel weui-media-box zdyadv">
  <div class="weui-panel__bd">
    <!--{eval echo stripcslashes($indexadv[1]['adv']);}-->
  </div>
</div>
<!--{/if}-->

<div class="weui-tab">
  <div class="weui-navbar" style="z-index: 5">
    <label class="weui-navbar__item weui-bar__item--on" id="bartab1">
      <!--{lang jameson_read:jingpintuijian}-->
    </label>
    <label class="weui-navbar__item" id="bartab2">
      <!--{lang jameson_read:xinshushangjia}-->
    </label>
    <label class="weui-navbar__item" id="bartab3">
      <!--{lang jameson_read:yuedubang}-->
    </label>
  </div>
  <div class="weui-tab__bd">
    <div id="ittab1" class="weui-tab__bd-item weui-tab__bd-item--active">
      <!--{if $paihang['tuijian']}-->
      <div class="rementushu">
        <div class="overflow-auto">
        {loop $paihang['tuijian'] $ikey $book}
          <a href="{eval echo mbreurl('book',$book['book_id']);}">
            <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
            <span>{$book['book_name']}</span>
          </a>
        {/loop}
        </div>
      </div>
        <!--{if $indexadv[2]['adv']}-->
        <div class="weui-panel weui-media-box zdyadv">
          <div class="weui-panel__bd">
            <!--{eval echo stripcslashes($indexadv[2]['adv']);}-->
          </div>
        </div>
        <!--{/if}-->
      <!--{else}-->
        <h2 class="weui-media-box__title">{lang jameson_read:zanwujilu}</h2>
      <!--{/if}-->
    </div>
    <div id="ittab2" class="weui-tab__bd-item">
      <!--{if $paihang['zuixin']}-->
        <div class="rementushu">
          <div class="overflow-auto">
          {loop $paihang['zuixin'] $ikey $book}
            <a href="{eval echo mbreurl('book',$book['book_id']);}">
              <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
              <span>{$book['book_name']}</span>
            </a>
          {/loop}
          </div>
        </div>
        <!--{if $indexadv[3]['adv']}-->
        <div class="weui-panel weui-media-box zdyadv">
          <div class="weui-panel__bd">
            <!--{eval echo stripcslashes($indexadv[3]['adv']);}-->
          </div>
        </div>
        <!--{/if}-->
      <!--{else}-->
        <h2 class="weui-media-box__title">{lang jameson_read:zanwujilu}</h2>
      <!--{/if}-->
    </div>
    <div id="ittab3" class="weui-tab__bd-item">
      <!--{if $paihang['yuedu']}-->
        <div class="rementushu">
          <div class="overflow-auto">
          {loop $paihang['yuedu'] $ikey $book}
            <a href="{eval echo mbreurl('book',$book['book_id']);}">
              <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
              <span>{$book['book_name']}</span>
            </a>
          {/loop}
          </div>
        </div>
        <!--{if $indexadv[4]['adv']}-->
        <div class="weui-panel weui-media-box zdyadv">
          <div class="weui-panel__bd">
            <!--{eval echo stripcslashes($indexadv[4]['adv']);}-->
          </div>
        </div>
        <!--{/if}-->
      <!--{else}-->
        <h2 class="weui-media-box__title">{lang jameson_read:zanwujilu}</h2>
      <!--{/if}-->
    </div>
  </div>
</div>
<!--专题1-->
{if $zhuanti[0]['books']}
<div class="rementushu">
  <div class="h_title mb10 mt10">
  <span class="text-danger">|</span>
  {$zhuanti[0]['name']}<em>{$zhuanti[0]['descro']}</em>
  </div>
  <div class="overflow-auto mt10">
    {loop $zhuanti[0]['books'] $key $book}
      <a href="{eval echo mbreurl('book',$book['book_id']);}">
        <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
        <span>{$book['book_name']}</span>
      </a>
    {/loop}
  </div>
</div>
{/if}


<!--{if $indexadv[5]['adv']}-->
<div class="weui-panel weui-media-box zdyadv">
  <div class="weui-panel__bd">
    <!--{eval echo stripcslashes($indexadv[5]['adv']);}-->
  </div>
</div>
<!--{/if}-->
<!--分类-->


    {loop $catedata $root}
        <div class="rementushu">
          <div class="h_title mb10 mt10">
            <span class="text-danger">|</span>
            <a class="" href="{eval echo mbreurl('category',$root['category_id']);}">
              <!--{$root['category_name']}-->
            </a>
          </div>
          <div class="overflow-auto">
          {loop $root['tuijian'] $ikey $book}
            <a href="{eval echo mbreurl('book',$book['book_id']);}">
              <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
              <span>{$book['book_name']}</span>
            </a>
          {/loop}
          </div>
        </div>
    {/loop}


  <!--{if $indexadv[6]['adv']}-->
  <div class="weui-panel weui-media-box zdyadv">
    <div class="weui-panel__bd">
      <!--{eval echo stripcslashes($indexadv[6]['adv']);}-->
    </div>
  </div>
  <!--{/if}-->

<!--专题2-->
{if $zhuanti[1]['books']}
<div class="rementushu">
  <div class="h_title mb10 mt10">
  <span class="text-danger">|</span>
  {$zhuanti[1]['name']}<em>{$zhuanti[1]['descro']}</em>
  </div>
  <div class="overflow-auto mt10">
    {loop $zhuanti[1]['books'] $key $book}
      <a href="{eval echo mbreurl('book',$book['book_id']);}">
        <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
        <span>{$book['book_name']}</span>
      </a>
    {/loop}
  </div>
</div>
{/if}
<!--{if $indexadv[7]['adv']}-->
<div class="weui-panel weui-media-box zdyadv">
  <div class="weui-panel__bd">
    <!--{eval echo stripcslashes($indexadv[7]['adv']);}-->
  </div>
</div>
<!--{/if}-->
<!--专题2-->
{if $zhuanti[2]['books']}
<div class="rementushu">
  <div class="h_title mb10 mt10">
  <span class="text-danger">|</span>
  {$zhuanti[2]['name']}<em>{$zhuanti[2]['descro']}</em>
  </div>
  <div class="overflow-auto mt10">
    {loop $zhuanti[2]['books'] $key $book}
      <a href="{eval echo mbreurl('book',$book['book_id']);}">
        <img src="{if $book['image']}https://ebookimage.hygx.org/{$book['image']}{else}{$_G['jameson_read']['morenfengmian']}{/if}" alt="{$book['book_name']}" />
        <span>{$book['book_name']}</span>
      </a>
    {/loop}
  </div>
</div>
{/if}
<!--{if $indexadv[8]['adv']}-->
<div class="weui-panel weui-media-box zdyadv">
  <div class="weui-panel__bd">
    <!--{eval echo stripcslashes($indexadv[8]['adv']);}-->
  </div>
</div>
<!--{/if}-->
<!--{if strpos($_SERVER['HTTP_USER_AGENT'], 'hygxAPP')}--><p style="text-align:center;font-size:x-small"><sub>广东华韵网络科技有限公司 版权所有 &copy; $_G['setting']['sitename']</sub><br><sup><a href="thread-337216-1-1.html">《用户协议》</a>&#x3000;&#x3000;<a href="thread-341353-1-1.html">《隐私政策》</a></sup></p><!--{/if}-->
<!--{subtemplate jameson_read:touch/footer}--><!--{template common/footer_hook}-->