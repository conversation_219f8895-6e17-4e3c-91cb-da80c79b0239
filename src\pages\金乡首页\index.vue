<template>
  <swiper
    class="banner"
    indicator-color="#ccc"
    indicator-active-color="#FF6600"
    :circular="true"
    :indicator-dots="true"
    :autoplay="false"
  >
    <swiper-item v-for="item in data.ajaxData.ad" @tap="open(item.url)">
      <image class="banner-image" :src="getUrl(item.image)" />
    </swiper-item>
  </swiper>
  <view class="index-info">
    <view class="info-item" @tap="open('/114_m_zj.asp')">
      <view class="info-item-title">
        <text>今日浏览量</text>
      </view>
      <view class="info-item-value">
        <text class="info-value-color">{{
          data.ajaxData.header_info.view
        }}</text>
        <text> 次</text>
      </view>
    </view>
    <view class="info-item-center"></view>
    <view class="info-item" @tap="open('/114_m_zj.asp')">
      <view class="info-item-title">
        <text>今日新信息</text>
      </view>
      <view class="info-item-value">
        <text class="info-value-color">{{
          data.ajaxData.header_info.new_info
        }}</text>
        <text> 条</text>
      </view>
    </view>
    <view class="info-item-center"></view>
    <view class="info-item" @tap="open('/dblist_top.asp')">
      <view class="info-item-title">
        <text>店铺收录</text>
      </view>
      <view class="info-item-value">
        <text class="info-value-color">{{
          data.ajaxData.header_info.shop_count
        }}</text>
        <text> 个</text>
      </view>
    </view>
  </view>
  <view class="index-nav">
    <view
      class="nav-item"
      v-for="item in data.ajaxData.menu"
      @tap="open(item.url)"
    >
      <view class="nav-icon">
        <image class="nav-image" :src="getUrl(item.image)" />
      </view>
      <view class="nav-title">
        <text>{{ item.title }}</text>
      </view>
    </view>
  </view>
  <scroll-view
    class="scroll-view_H"
    :scroll-x="true"
    @scroll="scroll($event)"
    style="width: 100%"
  >
    <view
      class="scroll-item"
      v-for="item in data.ajaxData.tools"
      @tap="open(item.url)"
    >
      <view class="scroll-icon">
        <image class="scroll-image" :src="getUrl(item.image)" />
      </view>
      <view class="scroll-title">
        <text>{{ item.title }}</text>
      </view>
    </view>
  </scroll-view>
  <view class="scroll-info">
    <view
      class="scroll-index"
      :style="{ 'margin-left': `${data.scrollWidth}px` }"
    ></view>
  </view>
  <view class="hr"></view>
  <swiper
    class="ad-banner"
    :circular="true"
    :indicator-dots="false"
    :autoplay="true"
  >
    <swiper-item v-for="item in data.ajaxData.ad_banner" @tap="open(item.url)">
      <image class="ad-banner-image" :src="getUrl(item.image)" />
    </swiper-item>
  </swiper>
  <view class="hr"></view>
  <view class="hot-title">
    <text>热门栏目推荐</text>
    <image
      class="hot-title-image"
      src="https://m.jinxiang114.com/114_img/hot2.jpg"
    />
  </view>
  <view class="hot-list">
    <view
      class="hot-item"
      v-for="item in data.ajaxData.hot_list"
      @tap="open(item.url)"
    >
      <view class="hot-left">
        <view class="hot-left-main">
          <text>{{ item.title }}</text>
        </view>
        <view class="hot-left-des">
          <text>{{ item.desc }}</text>
        </view>
      </view>
      <view class="hot-right">
        <image class="hot-image" :src="getUrl(item.image)" />
      </view>
    </view>
  </view>
  <view class="hr"></view>
  <view class="rc-title">
    <text>人才招聘信息</text>
  </view>
  <view class="rc-main-tags">
    <view
      class="rc-main-tag cy"
      @tap="
        open(
          '114_m_zj.asp?key=%B7%B9%B5%EA,%C9%FA%CC%AC%D4%B0,%BE%C6%B5%EA,%B2%CD%D2%FB,%B3%F8%CA%A6,%CF%B4%CD%EB%B9%A4,%BA%F3%B3%F8,%BB%F0%B9%F8,%BA%E6%B1%BA,%C3%C0%CA%B3,%B5%B0%B8%E2,%B4%AB%B2%CB&id=143&jd='
        )
      "
    >
      <text>餐饮类</text>
      <image
        class="rc-main-tag-image"
        src="https://m.jinxiang114.com/114_img/meishi.png"
      />
    </view>
    <view
      class="rc-main-tag jy"
      @tap="
        open(
          '114_m_zj.asp?key=%BD%CC%CA%A6,%C0%CF%CA%A6,%D3%D7%CA%A6,%D3%D7%BD%CC,%BD%CC%D3%FD,%D3%D7%B6%F9,%BF%CE%B3%CC&id=143&jd='
        )
      "
    >
      <text>教育业</text>
      <image
        class="rc-main-tag-image"
        src="https://m.jinxiang114.com/114_img/jiaoyu2.png"
      />
    </view>
    <view
      class="rc-main-tag xs"
      @tap="
        open(
          '114_m_zj.asp?id=143&key=%CF%FA%CA%DB,%D2%B5%CE%F1,%D3%AA%D2%B5%D4%B1,%B5%BC%B9%BA,%B5%EA%D4%B1'
        )
      "
    >
      <text>销售业</text>
      <image
        class="rc-main-tag-image"
        src="https://m.jinxiang114.com/114_img/gouwu.png"
      />
    </view>
    <view
      class="rc-main-tag fc"
      @tap="
        open(
          '114_m_zj.asp?key=%B5%D8%B2%FA,%B7%BF%B2%FA,%D6%C3%D2%B5,%CA%DB%C2%A5,%D6%C3%D2%B5%B9%CB%CE%CA,%CA%DB%C2%A5%D4%B1,%CA%DB%C2%A5%C8%CB%D4%B1&id=143&jd='
        )
      "
    >
      <text>房产业</text>
      <image
        class="rc-main-tag-image"
        src="https://m.jinxiang114.com/114_img/fangchan2019.png"
      />
    </view>
  </view>
  <view class="index-tags">
    <view
      class="index-tag"
      v-for="item in data.ajaxData.tag_list"
      @tap="open(item.url)"
    >
      <text>{{ item.title }}</text>
    </view>
  </view>
  <view class="hr"></view>
  <view class="hot-title">
    <text>金乡店铺推荐</text>
    <image
      class="hot-title-image"
      src="https://m.jinxiang114.com/114_img/hot2.jpg"
    />
  </view>
  <view class="tj-nav">
    <view
      class="nav-item"
      v-for="item in data.ajaxData.shop_list"
      @tap="open(item.url)"
    >
      <view class="nav-icon">
        <image class="nav-image" :src="getUrl(item.image)" />
      </view>
      <view class="nav-title">
        <text>{{ item.title }}</text>
      </view>
    </view>
  </view>
  <view class="index-tags">
    <view
      class="index-tag"
      v-for="item in data.ajaxData.shop_tag"
      @tap="open(item.url)"
    >
      <text>{{ item.title }}</text>
    </view>
  </view>
  <view class="hr"></view>
  <view class="rc-title">
    <text>房屋中介信息</text>
  </view>
  <view class="other-tags">
    <view
      class="other-tag"
      v-for="item in data.ajaxData.info_tag"
      @tap="open(item.url)"
    >
      <text>{{ item.title }}</text>
    </view>
  </view>
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const { screenWidth } = Taro.getSystemInfoSync();

    const data = reactive({
      scrollWidth: 0,
      ajaxData: {
        ad: [{ image: "114_gg/wlzph2.png", url: "wlzph.asp?id=143" }],
        header_info: { view: 179142, new_info: 806, shop_count: 8309 },
        menu: [
          { title: "发中介", url: "add.asp", image: "114_img/add.gif" },
          {
            title: "房产",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=144",
            image: "114_img/fanchan.png",
          },
          {
            title: "招聘",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=143",
            image: "114_img/zhaopin.png",
          },
          {
            title: "求职",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=142",
            image: "img_zj/qz2.gif?1",
          },
          {
            title: "二手",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=138",
            image: "114_img/es2.gif",
          },
        ],
        ad_banner: [{ image: "114_gg/dpqg.gif", url: "114_m_youhui.asp" }],
        hot_list: [
          {
            title: "高薪好工作",
            desc: "每天大量好职位",
            image: "114_img/zhaopin.png",
            url: "114_m_zj.asp?id=143",
          },
          {
            title: "大牌抢购",
            desc: "大牌抢购",
            image: "114_img/qg.jpg",
            url: "114_m_youhui.asp",
          },
          {
            title: "线上售楼处",
            desc: "金乡89个热销楼盘",
            image: "114_img/fc.png",
            url: "114_loupan.asp",
          },
          {
            title: "推荐好店铺",
            desc: "6000多个本地商家",
            image: "114_img/fangjia.png",
            url: "114_haodian.asp",
          },
          {
            title: "金牌房产销售",
            desc: "100多位金牌顾问",
            image: "114_img/guwen.png",
            url: "fangchan_guwen.asp",
          },
          {
            title: "相亲交友",
            desc: "帮您快速告别单身",
            image: "114_img/jy3.png",
            url: "love.asp",
          },
          {
            title: "人气商家",
            desc: "金乡人气商家评选",
            image: "114_img/top.png",
            url: "renqishangjia.asp",
          },
          {
            title: "热点资讯",
            desc: "最近热点资讯",
            image: "114_img/xinwen.png",
            url: "toutiao.asp",
          },
        ],
        tag_list: [
          { title: "全部", url: "https://mzj.jinxiang114.com/114_m_zj.asp" },
          {
            title: "家居建材",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=装饰,家居,装修,橱柜,卫浴,建材,安装工,沙发,家私,家具,衣柜&id=143",
          },
          {
            title: "餐饮",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=饭店,生态园,酒店,餐饮,厨师,洗碗工,后厨,火锅,烘焙,美食,蛋糕,传菜&id=143",
          },
          {
            title: "教育",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=教师,老师,幼师,幼教,教育,幼儿,课程&id=143",
          },
          {
            title: "房产",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=地产,房产,置业,售楼,置业顾问,售楼员,售楼人员&id=143",
          },
          {
            title: "销售",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=销售,业务,营业员,导购,店员&id=143",
          },
          {
            title: "前台",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=前台,收银,柜台,收款&id=143",
          },
          {
            title: "服务员",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=服务员,服务生,勤杂工,洗刷,保洁,家政,服务人员,保姆,杂工&id=143",
          },
          {
            title: "业务",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=业务&id=143",
          },
          {
            title: "营业员",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=营业员,导购,店员&id=143",
          },
          {
            title: "设计师",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=美工,平面,设计师,技术员&id=143",
          },
          {
            title: "超市",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=超市,商场,银座,贵和,购物,家电&id=143",
          },
          {
            title: "会计",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=会计,出纳,财务&id=143",
          },
          {
            title: "店长",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=店长,领班,大堂&id=143",
          },
          {
            title: "保安",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=保安,门卫,安保,押车,押运,门岗,看门&id=143",
          },
          {
            title: "兼职",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=兼职,宝妈&id=143",
          },
          {
            title: "家教",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=家教,辅导&id=143",
          },
          {
            title: "管理",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=大堂经理,管理,主管,店长,业务经理,人事主管,部门经理,总经理,经理助理&id=143",
          },
          {
            title: "司机",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=司机,驾驶员,C1,c1,A1,A2,B2,快递,派件,发货&id=143",
          },
          {
            title: "传单",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=传单&id=143",
          },
          {
            title: "客服",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=客服,电话客服,售后&id=143",
          },
          {
            title: "文员",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=文员,内勤&id=143",
          },
          {
            title: "工人",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=工人,装卸,拉货,加工,长期工,短期工,小时工,招工,劳务,施工,装工,车间,工厂,货工,普工,小工,送货工,学徒&id=143",
          },
          {
            title: "收银",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=收银&id=143",
          },
          {
            title: "假期工",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=暑假工,寒假工,假期工&id=143",
          },
          {
            title: "保险",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=人事助理,内勤,宝妈,兼职,中国人寿,中国平安,保险,国企,央企&id=143",
          },
          {
            title: "劳务",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=务工,派遣,小时工,计件,工厂,工人,装工,普工,高端就业,就业&id=143",
          },
          {
            title: "技术人才",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=技师,师傅,学徒,技术,老师,美容师,美发师,剪发,发型师,二保,铆工,电焊,摄影师,化妆师&id=143",
          },
          {
            title: "家政服务",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=水管道,电工,疏通,保洁,保姆,月嫂,钟点工,清洁,月子,开荒,搬家,清洗,护理,维修,洗车工,加油&id=143",
          },
          {
            title: "才艺",
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?key=主播,歌手,艺人,才艺,演员,舞蹈,跳舞,直播,抖音,快手,火山&id=143",
          },
        ],
        shop_list: [
          {
            title: "全部好店",
            url: "114_haodian.asp",
            image: "114_img/ershou.jpg",
          },
          {
            title: "餐饮美食",
            url: "114_haodian.asp?id=165",
            image: "/114_img/meishi.png",
          },
          {
            title: "好中介",
            url: "114_haodian.asp?id=398",
            image: "/114_img/zhongjie2.png",
          },
          {
            title: "教育",
            url: "114_haodian.asp?id=163",
            image: "/114_img/jiaoyu.png",
          },
          {
            title: "楼盘",
            url: "114_loupan.asp",
            image: "/114_img/fangchan2019.png",
          },
          {
            title: "家政保洁",
            url: "114_haodian.asp?id=219",
            image: "/114_img/jiazheng2.png",
          },
          {
            title: "好宾馆",
            url: "114_haodian.asp?id=167",
            image: "/114_img/chuang2.png",
          },
          {
            title: "装修",
            url: "114_haodian.asp?id=396",
            image: "/114_img/zhuangxiu2.png",
          },
          {
            title: "购物逛街",
            url: "114_haodian.asp?id=379",
            image: "/114_img/gouwu.png",
          },
          {
            title: "建材",
            url: "114_haodian.asp?id=311",
            image: "114_img/jianchai.png",
          },
          {
            title: "休闲娱乐",
            url: "114_haodian.asp?id=166",
            image: "/114_img/ktv2.png",
          },
          {
            title: "小区",
            url: "114_haodian.asp?id=728",
            image: "/114_img/xiaoqu.png",
          },
          {
            title: "汽车服务",
            url: "114_haodian.asp?id=224",
            image: "114_img/qichefuwu.png",
          },
          {
            title: "家居用品",
            url: "114_haodian.asp?id=308",
            image: "114_img/jiaju.png",
          },
          {
            title: "生活服务",
            url: "114_haodian.asp?id=172",
            image: "/114_img/jiazheng2.png",
          },
        ],
        shop_tag: [
          { url: "114_haodian.asp?id=213%27", title: "美容美发" },
          { url: "114_haodian.asp?id=394%27", title: "电脑监控" },
          { url: "114_haodian.asp?id=324%27", title: "快递物流" },
          { url: "114_haodian.asp?id=457%27", title: "美容减肥" },
          { url: "114_haodian.asp?id=395%27", title: "家电维修" },
        ],
        info_tag: [
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=143&jd=",
            title: "不限",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=145&zj=&key=",
            title: "出租",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=146&zj=&key=",
            title: "求租",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=462&zj=&key=",
            title: "出售",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=463&zj=&key=",
            title: "求购",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=736&zj=&key=",
            title: "售门面",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=737&zj=&key=",
            title: "买门面",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=735&zj=&key=",
            title: "门面求租",
          },
          {
            url: "https://mzj.jinxiang114.com/114_m_zj.asp?id=486&zj=&key=",
            title: "门面转租",
          },
        ],
        tools: [
          {
            title: "全部功能",
            url: "114_gongnengdaquan.asp",
            image: "114_img/fenlei4.png",
          },
          {
            title: "大牌抢购",
            url: "114_m_youhui.asp",
            image: "114_img/biao.png",
          },
          {
            title: "餐饮",
            url: "114_haodian.asp?id=165",
            image: "114_img/meishi.png",
          },
          {
            title: "家政",
            url: "114_haodian.asp?id=219",
            image: "114_img/jiazheng2.png",
          },
          {
            title: "楼盘",
            url: "114_loupan.asp",
            image: "114_img/fangchan2019.png",
          },
          {
            title: "教育",
            url: "114_haodian.asp?id=163",
            image: "114_img/jiaoyu.png",
          },
          {
            title: "装修",
            url: "114_haodian.asp?id=396",
            image: "114_img/zhuangxiu2.png",
          },
          {
            title: "顺风车",
            url: "114_m_zj_sfc.asp",
            image: "114_img/sfc.gif",
          },
          {
            title: "好中介",
            url: "114_haodian.asp?id=398",
            image: "114_img/zhongjie2.png",
          },
          {
            title: "好宾馆",
            url: "114_haodian.asp?id=167",
            image: "114_img/chuang2.png",
          },
          {
            title: "休闲娱乐",
            url: "114_haodian.asp?id=166",
            image: "114_img/ktv2.png",
          },
          {
            title: "小区",
            url: "114_haodian.asp?id=728",
            image: "114_img/xiaoqu.png",
          },
          {
            title: "购物逛街",
            url: "114_haodian.asp?id=379",
            image: "114_img/gouwu.png",
          },
          {
            title: "汽车服务",
            url: "114_haodian.asp?id=224",
            image: "114_img/qichefuwu.png",
          },
          {
            title: "家居用品",
            url: "114_haodian.asp?id=308",
            image: "114_img/jiaju.png",
          },
          {
            title: "家居建材",
            url: "114_haodian.asp?id=311",
            image: "114_img/jianchai.png",
          },
          { title: "相亲交友", url: "love.asp", image: "114_img/xiangqin.png" },
          {
            title: "公交查询",
            url: "https://m.jinxiang114.com/bus/index.asp?xapp-refresh=0",
            image: "114_img/gongjiao.png",
          },
          {
            title: "长途汽车",
            url: "https://www.jnjyjt.cn/",
            image: "114_img/gongjiao.png",
          },
          {
            title: "找厕所",
            url: "https://m.jinxiang114.com/zcs/index.asp?xapp-refresh=0",
            image: "114_img/wc.png",
          },
          {
            title: "停电信息",
            url: "https://mp.weixin.qq.com/s/EwJ4ErY-CRcDpoWJqJONsg",
            image: "114_img/tingdian.png",
          },
          {
            title: "大蒜报价",
            url: "dasuan.asp",
            image: "114_img/dasuan2.png",
          },
          {
            title: "天气预报",
            url: "https://m.weathercn.com/index.do?day=1&partner=&yemian=forecast_15days&id=60693&p_source=&p_type=jump",
            image: "114_img/tianqi.png",
          },
          {
            title: "快递查询",
            url: "https://m.kuaidi100.com/index.jsp?from=openv",
            image: "114_img/kuaidi.png",
          },
          {
            title: "违章查询",
            url: "https://0537.m.weizhangwang.com/",
            image: "114_img/weizhang.png",
          },
          { title: "微社区", url: "bbs_list.asp", image: "114_img/shequ.png" },
          {
            title: "热点资讯",
            url: "toutiao.asp",
            image: "114_img/xinwen.png",
          },
        ],
      },
    });
    const scroll = (e) => {
      data.scrollWidth =
        (e.detail.scrollLeft / (e.detail.scrollWidth - screenWidth)) *
        (48 / 750) *
        390;
    };
    const getUrl = (url) => {
      if (url.indexOf("http") == 0) {
        return url;
      }
      return (
        "https://m.jinxiang114.com" + (url.indexOf("/") == 0 ? "" : "/") + url
      );
    };
    const open = (url) => {
      console.log(url);
      Taro.navigateTo({
        url: "/pages/_ym/web?url=" + encodeURIComponent(getUrl(url)),
      });
    };
    onMounted(() => {
      Taro.request({
        url: "https://m.jinxiang114.com/index_2020_api.asp", //仅为示例，并非真实的接口地址
        header: {
          "content-type": "application/json", // 默认值
        },
        success: function (res) {
          if (res.statusCode == 200) {
            data.ajaxData = res.data;
          }
        },
      });
    });
    return {
      data,
      scroll,
      getUrl,
      open,
    };
  },
};
</script>

<style>
.banner {
  margin: 0px auto;
  width: 680px;
  height: 320px;
}

.banner-image {
  width: 680px;
  height: 260px;
  border-radius: 32px;
}

.index-info {
  display: flex;
  justify-content: space-around;
  margin: 12px 0;
}

.info-item {
  flex: 1;
  text-align: center;
  background: #fff;
  height: 102px;
  font-size: 32px;
}

.info-item-center {
  width: 1px;
  background: #ddd;
}

.info-item-title {
  color: rgb(175, 175, 175);
}

.info-item-value {
  margin: 10px 0;
  color: #3a5577;
}

.info-value-color {
  color: #ff0000;
}

.index-nav {
  border-bottom: 1px solid #f5f5f5;
  border-top: 1px solid #f5f5f5;
  display: flex;
  flex-wrap: wrap;
  margin: 0 auto;
  width: 684px;
  text-align: center;
  padding-bottom: 20px;
}

.nav-item {
  width: 119px;
  height: 154px;
  padding: 8px;
  font-size: 26px;
}

.nav-image {
  margin: 19px auto 16px;
  width: 80px;
  height: 80px;
  border-radius: 80px;
}

.nav-title {
  color: #3a5577;
}

.scroll-view_H {
  white-space: nowrap;
}

.scroll-item {
  display: inline-block;
  width: 141px;
  height: 102px;
  padding-top: 20px;
  font-size: 24px;

  text-align: center;
}

.scroll-item:first-child {
  margin-left: 20px;
}

.scroll-image {
  margin: 10px 42px;
  width: 60px;
  height: 60px;
}

.scroll-title {
  color: #3a5577;
}

.scroll-info {
  width: 60px;
  height: 6px;
  margin: 6px auto;
  background: #ccc;
  border-radius: 2px;
}

.scroll-index {
  background: #ff6600;
  width: 12px;
  height: 6px;
  border-radius: 6px;
}

.ad-banner {
  margin: 20px auto;
  width: 680px;
  height: 140px;
}

.ad-banner-image {
  width: 680px;
  height: 140px;
}

.hr {
  height: 20px;
  background: #f7f7f7;
}

.hot-title {
  display: flex;
  height: 70px;
  justify-content: center;
  line-height: 70px;
  font-size: 40px;
  font-weight: 700;
  color: #3a5577;
}

.hot-title-image {
  margin: 15px 6px;
  height: 40px;
  width: 30px;
}

.hot-list {
  display: flex;
  flex-wrap: wrap;
  background: #f7f7f7;
  margin: 20px 0;
}

.hot-item {
  display: flex;
  width: 325px;
  height: 100px;
  padding: 24px;
  background: #fff;
  margin-bottom: 2px;
}

.hot-left {
  flex: 1;
}

.hot-item:nth-child(2n-1) {
  margin-right: 2px;
}

.hot-item:nth-child(-n + 2) {
  margin-top: 2px;
}

.hot-left-main {
  font-size: 32px;
}

.hot-left-des {
  font-size: 26px;
  color: #c0c0c0;
  padding-top: 10px;
}

.hot-image {
  width: 80px;
  height: 80px;
}

.rc-title {
  height: 60px;
  line-height: 60px;
  font-size: 36px;
  font-weight: 700;
  margin-left: 20px;
}

.rc-main-tags {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  border-bottom: 1px solid #f7f7f7;
}

.rc-main-tag {
  display: flex;
  width: 160px;
  height: 58px;
  font-size: 24px;
  border-radius: 8px;
  line-height: 58px;
}

.rc-main-tag.cy {
  background: linear-gradient(to right, #e0fdfe, #ffffff, #ffffff);
  border: 1px solid #0cb8f2;
  color: #0caef0;
}

.rc-main-tag.jy {
  background: linear-gradient(to right, #ffede1, #ffffff, #ffffff);
  border: 1px solid #ff9900;
  color: #ff6600;
}

.rc-main-tag.xs {
  background: linear-gradient(to right, #fde7e1, #ffffff, #ffffff);
  border: 1px solid #e5161c;
  color: #ff0000;
}

.rc-main-tag.fc {
  background: linear-gradient(to right, #e8eeff, #ffffff, #ffffff);
  border: 1px solid #6788ff;
  color: #045bc0;
}

.rc-main-tag text {
  flex: 1;
  padding-left: 20px;
}

.rc-main-tag-image {
  margin: 9px;
  height: 40px;
  width: 40px;
}

.index-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  padding-bottom: 20px;
}

.index-tag {
  width: 130px;
  height: 68px;
  line-height: 68px;
  margin-bottom: 10px;
  font-size: 24px;
  color: #3a5577;
  text-align: center;
  border: 1px solid #f7f7f7;
  border-radius: 12px;
  background-color: #f3f3f3;
}

.tj-nav {
  display: flex;
  flex-wrap: wrap;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 20px;
  justify-content: space-evenly;
  border-top: 1px solid #f7f7f7;
}

.tj-nav .nav-item {
  height: 119px;
}

.tj-nav .nav-image {
  margin: 10px auto 6px;
  width: 60px;
  height: 60px;
}

.other-tags {
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 160px;
}

.other-tag {
  width: 120px;
  height: 64px;
  line-height: 64px;
  margin-bottom: 10px;
  font-size: 24px;
  color: #3a5577;
  text-align: center;
  border: 1px solid #f7f7f7;
  border-radius: 12px;
  background-color: #fff;
  margin-right: 10px;
}
</style>
