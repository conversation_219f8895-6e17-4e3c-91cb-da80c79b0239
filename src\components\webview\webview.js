import Tools from "../../tools";
import {
  getCurrentInstance,
  hideShareMenu,
  getCurrentPages
} from "@tarojs/taro";

function WebView(pageCode) {
  const me = {
    /** 页面代码 */
    code: pageCode,
    /** 当前页面 Vue 实例 */
    vue: null,
    /** webView postMessage 来的消息 */
    messages: [],
    /** 页面配置的原始固定 URL */
    rawUrl: null
  };

  //构造分享、收藏参数 https://developers.weixin.qq.com/miniprogram/dev/reference/api/Page.html#%E9%A1%B5%E9%9D%A2%E4%BA%8B%E4%BB%B6%E5%A4%84%E7%90%86%E5%87%BD%E6%95%B0
  const eventsArg = (e, action) => {
    const ctx = getCurrentInstance();
    let query =
      e && e.webViewUrl ? "url=" + encodeURIComponent(e.webViewUrl) : null;
    let path = ctx.router.path + (query ? "?" + query : "");
    let ret = {
      path,
      query
    };
    if (action) {
      //分享
    }
    console.log(ret);
    return ret;
  };
  /**
   * 处理 postMessage 数据
   * @param {any} data
   */
  const processMessage = data => {
    if (!data) return;
    if (process.env.TARO_ENV === "alipay") {
      //支付宝登录
      if (data.on == "onLogin") {
        const web = my.createWebViewContext("web");
        const opt = Object.assign(data.data || { scopes: "auth_base" }, {
          success(res) {
            web.postMessage({
              on: "onLogin",
              success: true,
              data: {
                code: res.authCode
              }
            });
          },
          fail(res) {
            web.postMessage({
              on: "onLogin",
              success: false,
              data: res
            });
          }
        });
        console.log("alipayOnLogin", opt);
        my.getAuthCode(opt);
        return;
      }
      data = [data];
    }
    me.messages = data;
  };

  const onYmMessage = (name, success, data, url) => {
    //console.log("onYmMessage", name, success, data, url);
    if (url) {
      const cbs = [
        {
          on: name,
          success: success,
          data: Object.assign(data || {}, { _: new Date().getTime() })
        }
      ];
      me.vue.url = `${url}#xapp-mp-cbs=${encodeURIComponent(
        JSON.stringify(cbs)
      )}`;
    }
  };

  //onLoad
  this.onLoad = e => {
    //处理页面事件
    const pages = getCurrentPages();
    pages[pages.length - 1].onYmMessage = onYmMessage;

    e = Tools.ensureParameter(e);
    console.log("onLoad", e);
    if (e && e.url && e.url.indexOf("http:") == 0) {
      e.url = "https:" + e.url.substr(5);
    }
    //启动链接
    me.vue.url = (e && e.url) || me.rawUrl;
    //按需隐藏分享按钮
    if (hideShareMenu) {
      const ctx = getCurrentInstance();
      const cfg = ctx.page.config;
      const menus = [];
      !cfg.enableShareAppMessage && menus.push("shareAppMessage");
      !cfg.enableShareTimeline && menus.push("shareTimeline");
      menus.length && hideShareMenu({ menus });
    }
  };
  //onShow
  this.onShow = () => {};
  //收藏
  this.onAddToFavorites = e => {
    return eventsArg(e);
  };
  //转发好友
  this.onShareAppMessage = e => {
    return eventsArg(e, "onShareAppMessage");
  };
  //转发朋友圈
  this.onShareTimeline = () => {
    return eventsArg(null, "onShareTimeline");
  };

  //初始绑定数据
  this.data = () => {
    const page = me.code || null;
    //json string clone
    const data = JSON.parse(JSON.stringify((page && page.data) || {}));
    me.rawUrl = data.url;
    data.url = undefined;
    return data;
  };
  //web-view bind event
  this.methods = {
    handleMessage(e) {
      console.log("handleMessage", e);
      if (e && e.detail) {
        processMessage(e.detail.data);
      }
    }
  };
  //Vue instance
  this.setVue = vue => {
    me.vue = vue;
  };
}

export default {
  create(pageCode) {
    return new WebView(pageCode);
  }
};
