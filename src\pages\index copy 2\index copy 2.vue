<template>
  <view class="empty" v-if="data.activeid == 0 || data.placeid == 0">
    <text>请扫描二维码进入</text>
  </view>
  <view
    v-else
    class="main"
    :style="{ backgroundImage: `url('${imgUrl(data.activity.bg)}')` }"
  >
    <image class="logo" :src="imgUrl(data.activity.logo)"></image>
    <view class="title-name">
      <text>{{ data.activity.title }}</text>
    </view>
    <view class="title-des">
      <text>{{ data.activity.des }}</text>
    </view>
    <view class="addr-name">
      <text>{{ data.activity.place }}</text>
    </view>
    <view class="wel-name">
      <text>— 欢 迎 你 —</text>
    </view>
    <view class="floor-name" v-if="data.activity.floor > 0">
      <text>({{ data.activity.floor }}楼)</text>
    </view>
    <view v-if="data.type == 'init'">
      <button
        class="sign-btn"
        open-type="getPhoneNumber"
        @getphonenumber="getTel"
      >
        立即签到
      </button>
    </view>
    <view v-else>
      <view class="sign-tip" v-if="data.activity.canRollNum == 0">
        <text>您已签到成功！</text>
      </view>
      <view class="plate-wrap-box" v-else>
        <view class="plate-border" :animation="data.aniData">
          <image class="iconWheelCircle" :src="iconWheelCircle" />
          <view class="plate-wrap">
            <view
              v-for="(item, index) in data.activity.lottery"
              class="plate-box"
              key
              :style="
                'top:-' +
                  (data.activity.lottery.length - 6 <= 2
                    ? 36 + 4 * (data.activity.lottery.length - 6)
                    : 50) +
                  'rpx;transform-origin: 50% ' +
                  (data.activity.lottery.length - 6 <= 2
                    ? 256 + 4 * (data.activity.lottery.length - 6)
                    : 270) +
                  'rpx;border-top: ' +
                  (data.activity.lottery.length - 6 <= 2
                    ? 256 + 4 * (data.activity.lottery.length - 6)
                    : 270) +
                  'rpx solid #' +
                  (index % 2 == 0 ? 'ffffff' : 'ff4f5f') +
                  ';transform:translate(-50%,0) rotate(' +
                  (360 / data.activity.lottery.length) * index +
                  'deg);border-left: ' +
                  (440 / data.activity.lottery.length) * 2 +
                  'rpx solid transparent;border-right: ' +
                  (440 / data.activity.lottery.length) * 2 +
                  'rpx solid transparent;'
              "
            >
              <text
                class="text-box"
                :style="'color:#' + (index % 2 == 0 ? 'ff4f5f' : 'ffffff')"
              >
                {{ item }}
              </text>
            </view>
          </view>
        </view>

        <view class="plate-btn-wrap" @tap="startRollTap">
          <image class="iconWheelStart" :src="iconWheelStart" />
        </view>
      </view>
    </view>
    <view class="wheel-modal" v-if="data.visibleModal">
      <view class="wheel-modal-mask"></view>
      <view class="wheel-modal-content">
        <image
          class="modal-img"
          :src="data.isWin ? iconWheelModalSuccess : iconWheelModalFail"
        />
        <view class="modal-text">
          <text>{{
            data.isWin ? `您获得${data.prizeName}` : "很遗憾您没有中奖"
          }}</text>
        </view>
        <view class="modal-btnGroup" v-if="data.isWin">
          <view class="modal-btn-giveUp" @tap="closeModal">
            <text>放弃奖品</text>
          </view>
          <view
            :class="[
              data.prizeType == 0 || data.prizeType == 1
                ? 'modal-btn-get-short'
                : '',
              data.prizeType == 2 ? 'modal-btn-get-long' : ''
            ]"
            @tap="showHistoryModal(true)"
          >
            <text>
              {{ data.prizeType == 2 ? "联系客服领取奖品" : "立即领取" }}
            </text>
          </view>
        </view>
        <view class="modal-btnGroup" v-else>
          <view class="modal-btn-continue" @tap="closeModal">
            <text>继续努力</text>
          </view>
        </view>
      </view>
    </view>
    <view class="history-modal" v-if="data.visibleHistoryModal">
      <view class="history-modal-mask"></view>
      <view class="history-modal-content">
        <image
          @tap="showHistoryModal(false)"
          class="iconWheelModalClose"
          :src="iconWheelModalClose"
        />
        <view class="his-wel" v-show="!data.showAbout">
          <view class="his-tit">
            <text>恭喜您! 获得:</text>
          </view>
          <view class="his-box">
            <image class="gif2" :src="gif2"></image>
            <view class="his-lot">
              <text>{{ data.activity.historyData.name }}</text>
            </view>
            <view class="his-sta">
              <text>
                奖品是否领取:
              </text>
              <text
                :style="{
                  color: data.activity.historyData.status == 1 ? 'red' : '#000'
                }"
                >{{
                  data.activity.historyData.status == 1 ? "已领取" : "还未领取"
                }}</text
              >
            </view>
          </view>
        </view>
        <view class="his-qr" v-show="!data.showAbout">
          <view class="his-tip">
            <text>兑奖二维码</text>
          </view>
          <view class="his-tip">
            <text>(请勿外传，妥善保管，兑奖时出示)</text>
          </view>
          <view class="his-i">
            <image
              class="his-img"
              :src="getQr(data.activity.historyData.code)"
            ></image>
            <view
              class="his-img-s"
              v-if="data.activity.historyData.status == 1"
            >
              已领取
            </view>
          </view>
          <view class="his-btn">
            <button class="his-abo" size="mini" @tap="data.showAbout = true">
              兑奖须知
            </button>
          </view>
          <image class="gif1" :src="gif1"></image>
        </view>
        <view class="his-tips" v-show="data.showAbout">
          <view class="his-ati">
            <text>兑奖须知</text>
          </view>
          <view class="his-con">
            <text>
              {{ data.activity.lotteryAbout }}
            </text>
          </view>
          <view class="his-gif">
            <image class="gif11" :src="gif1"></image>
          </view>
          <view class="his-line"> </view>
        </view>
      </view>
    </view>
    <view class="menu">
      <view class="menu-item" @tap="showHistoryModal(true)"
        ><text>我的礼品</text></view
      >
      <view class="menu-item cccc" v-if="data.activity.mobile != ''" @tap="call"
        ><text>咨询电话</text>
      </view>
      <view
        class="menu-item eeee"
        v-if="data.activity.link != ''"
        @tap="open(data.activity.link)"
      >
        <view v-for="name in data.activity.link_name">
          <text>{{ name }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro, { useLoad } from "@tarojs/taro";

export default {
  setup() {
    const baseUrl = "https://demo.yimenseo.com/";
    const iconWheelBg = baseUrl + "static/<EMAIL>";
    const iconWheelCircle = baseUrl + "static/<EMAIL>";
    const iconWheelModalFail = baseUrl + "static/<EMAIL>";
    const iconWheelModalSuccess = baseUrl + "static/<EMAIL>";
    const iconWheelRuleBg = baseUrl + "static/<EMAIL>";
    const iconWheelStart = baseUrl + "static/<EMAIL>";
    const iconWheelTitle = baseUrl + "static/<EMAIL>";
    const iconWheelModalClose = baseUrl + "static/icon-close.png";
    const gif1 = baseUrl + "static/gif1.png";
    const gif2 = baseUrl + "static/gif2.png";
    // type 页面类型  init 需要获取code 和手机号  如果是  sign  就进入自动签到场景
    // const info = Taro.getEnterOptionsSync();
    const data = reactive({
      activeid: 1,
      placeid: 5,
      openid: "",
      type: "init",
      latitude: 0,
      longitude: 0,
      activity: {
        logo: "",
        bg: "",
        title: "",
        des: "",
        mobile: "",
        mobile_status: false,
        location_status: false,
        link: "",
        link_name: [],
        floor: 0,
        place: "",
        hasSign: false,
        canRollNum: 0,
        lottery: [],
        historyData: {
          name: "谢谢参与",
          code: "",
          status: 0
        }
      },
      aniData: null,
      rollState: false, // 是否正在抽奖
      visibleModal: false, //抽奖弹窗
      visibleHistoryModal: false, //中奖历史弹窗
      prizeType: 0, // 奖品类型 0-优惠券1-积分2-实物
      prizeName: null,
      isWin: false, //是否中奖
      num: 1, //用在动画上，让用户在第二次点击的时候可以接着上次转动的角度继续转
      showAbout: false
    });

    const imgUrl = url => {
      return baseUrl + "storage/" + url;
    };
    const getQr = url => {
      return baseUrl + "api/qr?c=" + url;
    };
    const getTel = e => {
      if (data.activity.location_status && data.longitude == 0) {
        Taro.showToast({
          title: "获取定位信息失败",
          icon: "error",
          duration: 2000
        });
      }
      console.log(e.detail);
      if (!e.detail.encryptedData) {
        Taro.showToast({
          title: "签到失败,请授权手机号后继续",
          icon: "error",
          duration: 2000
        });
        return;
      }
      let { encryptedData, iv } = e.detail;
      Taro.login({
        success(res) {
          let code = res.code;
          Taro.request({
            url: baseUrl + "api/sign",
            method: "POST",
            data: {
              id: data.activeid,
              pid: data.placeid,
              code,
              encryptedData,
              iv,
              latitude: data.latitude,
              longitude: data.longitude
            },
            success(ret) {
              if (ret.statusCode == 200) {
                if (ret.data.code != 1) {
                  Taro.showToast({
                    title: ret.data.message,
                    icon: "error",
                    duration: 2000
                  });
                  return;
                }
                data.type = "success";
                Taro.showToast({
                  title: ret.data.message,
                  icon: "success",
                  duration: 2000
                });
                Taro.setStorage({
                  key: "openid",
                  data: ret.data.data.openid
                });
                if (data.openid == "") {
                  data.openid = ret.data.data.openid;
                  getInfo();
                }
              }
            }
          });
        }
      });
    };
    const getLocation = () => {
      if (!data.activity.location_status) {
        reSign();
      }
      Taro.getLocation({
        type: "wgs84",
        success: function(res) {
          if (res.latitude) {
            data.latitude = res.latitude;
            data.longitude = res.longitude;
            if (data.type == "auto_sign") {
              reSign();
            }
          } else {
            if (data.activity.location_status) {
              Taro.showToast({
                title: "获取定位信息失败",
                icon: "error",
                duration: 2000
              });
              getLocation();
            }
          }
        },
        fail: function() {
          if (data.activity.location_status) {
            Taro.showToast({
              title: "获取定位信息失败",
              icon: "error",
              duration: 2000
            });
            getLocation();
          }
        }
      });
    };
    const reSign = () => {
      Taro.showLoading({
        title: "签到中"
      });

      Taro.request({
        url: baseUrl + "api/reSign",
        method: "POST",
        data: {
          id: data.activeid,
          pid: data.placeid,
          openid: data.openid,
          latitude: data.latitude,
          longitude: data.longitude
        },
        success(ret) {
          if (ret.statusCode == 200) {
            if (ret.data.code != 1) {
              data.type = "init";
              Taro.hideLoading();
              return;
            }
            setTimeout(() => {
              Taro.hideLoading();
              data.type = "success";
              Taro.showToast({
                title: ret.data.message,
                icon: "success",
                duration: 2000
              });
              data.activity.hasSign = true;
            }, 2000);
          }
        }
      });
    };
    const getInfo = () => {
      Taro.request({
        url: baseUrl + "api/getInfo",
        method: "POST",
        data: {
          id: data.activeid,
          pid: data.placeid,
          openid: data.openid
        },
        success(res) {
          if (res.statusCode == 200 && res.data.code == 1) {
            data.activity = res.data.data;
            if (data.activity.hasSign) {
              data.type = "success";
            } else {
              if (data.openid != "") {
                data.type = "auto_sign";
              }
            }
            getLocation();
          }
        }
      });
    };

    onMounted(() => {
      Taro.getStorage({
        key: "openid",
        success: function(res) {
          if (res.data) {
            data.openid = res.data;
          }
          getInfo();
        },
        fail: function() {
          getInfo();
        }
      });
      data.aniData = Taro.createAnimation({
        //创建动画对象
        duration: 3000,
        timingFunction: "ease"
      });
    });
    const showModal = () => {
      console.log("抽奖结束---显示弹窗");
    };
    const closeModal = () => {
      data.visibleModal = false;
    };
    const showHistoryModal = flag => {
      if (flag && data.activity.historyData.code == "") {
        Taro.showToast({
          title: "暂无礼品",
          icon: "error",
          duration: 2000
        });
        return;
      }
      if (
        flag &&
        data.activity.historyData &&
        data.activity.historyData.status == 0
      ) {
        getInfo();
      }
      data.visibleModal = false;
      data.visibleHistoryModal = flag;
      if (!flag) {
        data.showAbout = false;
      }
    };
    const startRollTap = () => {
      //开始转盘
      Taro.request({
        url: baseUrl + "api/lotter",
        method: "POST",
        data: {
          id: data.activeid,
          pid: data.placeid,
          openid: data.openid
        },
        success(ret) {
          if (ret.statusCode == 200) {
            if (ret.data.code != 1) {
              Taro.showToast({
                title: ret.data.message,
                icon: "error",
                duration: 2000
              });
              return;
            }
            const { lottery, canRollNum } = data.activity;
            const { rollState, visibleModal, num, isWin, aniData } = data;
            const lotteryArrLen = lottery.length;
            if (canRollNum < 1 || rollState || visibleModal) return;
            let rightNum = ret.data.data;
            // 随机次数为2,5,7则未中奖
            const lname = lottery[rightNum];
            if (lname == "谢谢参与") {
              data.isWin = false;
            } else {
              data.isWin = true;
              data.prizeName = lname;
            }
            console.log(`奖品是：${lottery[rightNum]}`);
            data.aniData
              .rotate(3600 * num - (360 / lotteryArrLen) * rightNum)
              .step(); //设置转动的圈数
            data.rollState = true;
            data.aniData = data.aniData.export(); //导出动画队列。export 方法每次调用后会清掉之前的动画操作。
            // 动画停止设置状态
            setTimeout(_ => {
              getInfo();
              showModal();
              data.visibleModal = true;
              data.activity.canRollNum = canRollNum - 1; // 抽奖次数减一
              data.rollState = false; // 将转盘状态切换为可抽奖
              data.num = num + 1;
              data.aniData = Taro.createAnimation({
                //创建动画对象
                duration: 3000,
                timingFunction: "ease"
              });
            }, 3000);
          }
        }
      });
    };
    const call = () => {
      Taro.makePhoneCall({
        phoneNumber: data.activity.mobile //仅为示例，并非真实的电话号码
      });
    };
    const open = url => {
      Taro.navigateTo({
        url: "/pages/_ym/web?url=" + encodeURIComponent(url)
      });
    };

    return {
      imgUrl,
      iconWheelBg,
      iconWheelCircle,
      iconWheelModalFail,
      iconWheelModalSuccess,
      iconWheelRuleBg,
      iconWheelStart,
      iconWheelTitle,
      iconWheelModalClose,
      data,
      getTel,
      showModal,
      closeModal,
      showHistoryModal,
      startRollTap,
      call,
      getQr,
      open,
      gif1,
      gif2
    };
  }
};
</script>

<style>
.main {
  height: 100vh;
  width: 100vw;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
}
.logo {
  margin: 5vw 0 5vw 5vw;
  width: 18.7vw;
  height: 13.7vw;
}

.title-name {
  color: #fff;
  text-align: center;
  font-weight: 700;
  font-size: 45px;
}
.title-des {
  color: #fff;
  text-align: center;
  font-size: 30px;
}
.addr-name {
  color: #fff;
  text-align: center;
  margin-top: 50px;
  font-weight: 700;
  font-size: 40px;
  background: linear-gradient(to right, #ffffff00, #00a2ea, #00a2ea, #ffffff00);
  width: 66vw;
  margin-left: 17vw;
  letter-spacing: 10px;
}
.wel-name {
  color: #fff;
  text-align: center;
  font-size: 35px;
  margin-top: 10px;
}
.floor-name {
  color: #fff;
  text-align: center;
  font-size: 28px;
}
.sign-tip {
  color: #fff;
  text-align: center;
  font-weight: 700;
  margin-top: 200px;
  font-size: 50px;
}
.menu {
  position: fixed;
  bottom: 0;
  height: 80px;
  width: 100vw;
  line-height: 80px;
  background: #e6e6e6;
  display: flex;
  z-index: 1000;
}
.menu .menu-item {
  flex: 1;
  text-align: center;
  font-size: 28px;
  color: #4d4d4d;
}
.eeee {
  line-height: 40px;
}
.cccc {
  border-left: 1px solid #b9b9b9;
  border-right: 1px solid #b9b9b9;
}
.sign-btn {
  margin: 100px 200px;
}
page {
  width: 100%;
  height: 100%;
}

.container {
  z-index: 1;
  height: 100%;
}
.iconWheelBg {
  width: 750px;
  height: 1750px;
  z-index: -1;
  position: absolute;
}
.history {
  width: 750px;
  height: 35px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  text-decoration: underline;
  color: rgba(255, 255, 255, 1);
  line-height: 35px;
  text-decoration: underline;
  position: absolute;
  text-align: right;
  top: 30px;
  right: 20px;
}
.title {
  width: 750px;
  height: 80px;
  font-size: 70px;
  font-family: PingFang SC;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  line-height: 80px;
  text-align: center;
  letter-spacing: 8px;
  position: absolute;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  top: 95px;
}
.rollNum {
  width: 750px;
  height: 30px;
  font-size: 29px;
  font-family: PingFang SC;
  font-weight: 100;
  color: rgba(255, 255, 255, 1);
  line-height: 30px;
  text-align: center;
  letter-spacing: 4px;
  position: absolute;
  top: 190px;
}
.plate-wrap-box {
  position: relative;
  z-index: 999;
  width: 750px;
  height: 678px;
  border-radius: 50%;
  top: 50px;
  text-align: center;
  display: flex;
  justify-content: center;
}
.iconWheelCircle {
  width: 678px;
  height: 678px;
  z-index: 9;
  position: relative;
  border-radius: 50%;
}
.plate-border {
  width: 678px;
  height: 678px;
  position: relative;
  display: flex;
  justify-content: center;
}

.plate-wrap {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
  width: 440px;
  height: 440px;
  border: 5px solid #f8ea6e;
  border-radius: 50%;
  overflow: hidden;
  margin: auto;
}

.plate-light {
  z-index: 1;
  width: 500px;
  height: 500px;
  border: none;
}

.plate-box {
  position: absolute;
  z-index: 1;
  left: 50%;
  width: 0;
  height: 0;
  font-size: 24px;
}

.bulb {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 50%;
  top: -242px;
  transform: translate(-50%, 0);
  border-radius: 50%;
  background-color: #fff0c7;
  filter: blur(0.5px);
}

.text-box {
  position: absolute;
  text-align: center;
  display: inline-block;
  width: 140px;
  word-break: break-all;
  top: -200px;
  transform: translate(-100%, 0);
  font-size: 19px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  left: 70px;
}

.plate-btn-wrap {
  position: absolute;
  z-index: 10;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 126px;
  height: 149px;
  border-radius: 50%;
}
.iconWheelStart {
  width: 126px;
  height: 149px;
  position: relative;
  z-index: 11;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.detial-wrap {
  position: absolute;
  z-index: 10;
  left: 50%;
  top: 950px;
  transform: translate(-50%, -50%);
  width: 750px;
  height: 630px;
  border-radius: 50%;
}
.iconWheelTitle {
  width: 400px;
  height: 110px;
  top: 75px;
  position: relative;
}
.detail-text {
  width: 250px;
  height: 95px;
  font-size: 45px;
  font-family: Source Han Sans CN;
  font-weight: 800;
  color: #a13d01;
  line-height: 80px;
  position: absolute;
  top: 80px;
  left: 255px;
  text-align: center;
  letter-spacing: 5px;
}
.iconWheelRuleBg {
  width: 600px;
  height: 600px;
}
.rule-detail {
  width: 520px;
  height: 480px;
  font-size: 28px;
  font-family: Source Han Sans CN;
  font-weight: 550;
  color: #f52c32;
  overflow: hidden;
  position: absolute;
  top: 200px;
  left: 120px;
  text-align: start;
}

.wheel-modal {
  width: 100vw;
  height: 85vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
.wheel-modal-mask {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.wheel-modal-content {
  width: 750px;
  height: 450px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.modal-img {
  width: 705px;
  height: 520px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.modal-text {
  height: 60px;
  line-height: 60px;
  width: 100%;
  text-align: center;
  position: absolute;
  top: 265px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
}
.modal-btnGroup {
  width: 750px;
  height: 80px;
  display: flex;
  justify-content: center;
  position: absolute;
  top: 355px;
}
.modal-btn-giveUp {
  width: 224px;
  height: 80px;
  background: rgba(255, 255, 255, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  margin-right: 10px;
}
.modal-btn-get-short {
  width: 224px;
  height: 80px;
  background: rgba(248, 228, 55, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
  margin-left: 10px;
}
.modal-btn-get-long {
  width: 290px;
  height: 80px;
  background: rgba(248, 228, 55, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
  margin-left: 10px;
}
.modal-btn-continue {
  width: 224px;
  height: 80px;
  background: rgba(255, 255, 255, 1);
  border-radius: 40px;
  text-align: center;
  line-height: 80px;
  font-size: 32px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(243, 59, 61, 1);
}

.history-modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
.history-modal-mask {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  background-color: rgba(0, 0, 0, 0.1);
}
.history-modal-content {
  width: 605px;
  height: 74vh;
  position: absolute;
  background: rgba(255, 255, 255, 1);
  border-radius: 5px;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  display: flex;
  flex-direction: column;
}
.iconWheelModalClose {
  width: 64px;
  height: 64px;
  position: absolute;
  top: -100px;
  right: 0;
}
.his-wel {
  background: #9a99ff;
  height: 350px;
}
.his-tit {
  font-weight: 700;
  font-size: 45px;
  color: #0001fd;
  margin: 45px 0 0 50px;
  letter-spacing: 8px;
}
.his-box {
  background: #dedffe;
  height: 205px;
  margin: 22px 100px;
  border-radius: 20px;
  text-align: center;
  position: relative;
}
.his-lot {
  color: red;
  font-weight: 700;
  font-size: 35px;
  padding: 50px 0;
  letter-spacing: 8px;
}
.his-sta {
  font-size: 20px;
  font-weight: 500;
}
.his-qr {
  background: #e6e6e6;
  flex: 1;
  padding-top: 30px;
  position: relative;
}
.his-tip {
  text-align: center;
  font-size: 20px;
  font-weight: 500px;
}
.his-img {
  height: 240px;
  width: 240px;
  background: #fff;
  margin: 80px 182.5px;
}
.his-i {
  position: relative;
}
.his-img-s {
  position: absolute;
  top: 0;
  height: 240px;
  width: 240px;
  margin: 80px 182.5px;
  line-height: 240px;
  text-align: center;
  color: red;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
}
.his-btn {
  display: flex;
  justify-content: center;
}
.his-abo {
  margin: 0 auto;
  background: linear-gradient(to top, #fdd8bb, #f6c99f, #f79937, #faef9d);
  border-radius: 25px;
  font-weight: 500;
}

.his-tips {
  background: #e6e6e6;
  flex: 1;
  padding: 100px;
  display: flex;
  flex-direction: column;
}
.his-ati {
  font-size: 35px;
  font-weight: 600;
  line-height: 40px;
  text-align: center;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    #ff6500,
    #ff6500,
    #ff6500,
    #ff6500,
    #ff6500,
    #ff6500
  );
  padding-bottom: 10px;
  letter-spacing: 8px;
  margin: 30px 0;
}
.his-con {
  margin-top: 60px;
  text-align: left;
  font-size: 20px;
  line-height: 40px;
  height: 550px;
  overflow: auto;
}
.his-gif {
  height: 150px;
  display: flex;
  justify-content: center;
}
.his-line {
  height: 10px;
  background: #ff6500;
}
.gif2 {
  position: absolute;
  top: -105px;
  right: -112px;
  width: 284.5px;
  height: 303.5px;
}
.gif1 {
  position: absolute;
  bottom: -70px;
  left: -70px;
  width: 300.5px;
  height: 300.5px;
}
.gif11 {
  width: 300.5px;
  height: 300.5px;
  position: relative;
  top: -120px;
}

.empty {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 背景渐变色 - 原理2 */
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  /* 背景尺寸 - 原理3 */
  background-size: 600% 600%;
  /* 循环动画 - 原理4 */
  animation: gradientBG 5s ease infinite;
  text-align: center;
  line-height: 100vh;
  font-weight: 700;
}

/* 动画，控制背景 background-position */
@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
