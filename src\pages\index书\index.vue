<template>
  <view class="main">
    <view class="header-search">
      <view class="header-image" @tap="open('ebook/shenqing')">
        <image :src="getUrl('miniApp/tl.png')" />
      </view>
      <view class="search-box" @tap="open('ebook/search')">
        <icon size="16" type="search" />
        <text>搜书</text>
      </view>
      <view class="header-image" @tap="open('ebook/bookstore/')">
        <image :src="getUrl('miniApp/tr.png')" />
      </view>
    </view>
    <view class="menu">
      <view
        class="menu-item"
        v-for="item in data.menu"
        :key="item.title"
        @tap="open(item.url)"
      >
        <view class="menu-icon">
          <image :src="getUrl(item.icon)" />
        </view>
        <view class="menu-text">
          <text>{{ item.title }}</text>
        </view>
      </view>
    </view>
    <view class="book-list" v-if="data.historys.length > 0">
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title">最近阅读</text>
      </view>
      <scroll-view
        class="scroll-view_H"
        :enhanced="true"
        :show-scrollbar="true"
        :scroll-x="true"
        style="width: 100%"
      >
        <view
          class="scroll-item"
          v-for="item in data.historys"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
          <view class="scroll-title">
            <text>{{ item.subject }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="book-list">
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title">热门图书</text>
      </view>
      <scroll-view
        class="scroll-view_H"
        :enhanced="true"
        :show-scrollbar="true"
        :scroll-x="true"
        style="width: 100%"
      >
        <view
          class="scroll-item"
          v-for="item in data.paihang.haoping"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="list-choose">
      <view
        :class="['choose-item', data.active == 'tuijian' ? 'active' : '']"
        @tap="data.active = 'tuijian'"
        ><text>精品推荐</text></view
      >
      <view
        :class="['choose-item', data.active == 'zuixin' ? 'active' : '']"
        @tap="data.active = 'zuixin'"
        ><text>新书上架</text></view
      >
      <view
        :class="['choose-item', data.active == 'yuedu' ? 'active' : '']"
        @tap="data.active = 'yuedu'"
        ><text>阅读榜</text></view
      >
    </view>
    <view class="book-list">
      <scroll-view class="scroll-view_H" :scroll-x="true" style="width: 100%">
        <view
          class="scroll-item"
          v-for="item in data.paihang[data.active]"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="book-list" v-if="data.zhuanti[0] && data.zhuanti[0].books">
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title">{{ data.zhuanti[0].name }}</text>
        <text class="header-tip">{{ data.zhuanti[0].descro }}</text>
      </view>
      <scroll-view class="scroll-view_H" :scroll-x="true" style="width: 100%">
        <view
          class="scroll-item"
          v-for="item in data.zhuanti[0].books"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <view
      class="book-list"
      v-for="cate in data.catedata"
      @tap.stop="open('ebook/shaixuan/category_id/' + cate.category_id)"
    >
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title c777">{{ cate.category_name }}</text>
      </view>
      <scroll-view class="scroll-view_H" :scroll-x="true" style="width: 100%">
        <view
          class="scroll-item"
          v-for="item in cate.tuijian"
          @tap.stop="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="book-list" v-if="data.zhuanti[1] && data.zhuanti[1].books">
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title">{{ data.zhuanti[1].name }}</text>
        <text class="header-tip">{{ data.zhuanti[1].descro }}</text>
      </view>
      <scroll-view class="scroll-view_H" :scroll-x="true" style="width: 100%">
        <view
          class="scroll-item"
          v-for="item in data.zhuanti[1].books"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="book-list" v-if="data.zhuanti[2] && data.zhuanti[2].books">
      <view class="list-header">
        <text class="header-line">|</text>
        <text class="header-title">{{ data.zhuanti[2].name }}</text>
        <text class="header-tip">{{ data.zhuanti[2].descro }}</text>
      </view>
      <scroll-view class="scroll-view_H" :scroll-x="true" style="width: 100%">
        <view
          class="scroll-item"
          v-for="item in data.zhuanti[2].books"
          @tap="goBook(item.book_id)"
        >
          <view class="scroll-icon">
            <image class="scroll-image" :src="getImageUrl(item.image)" />
          </view>
          <view class="scroll-title">
            <text>{{ item.book_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view style="height: 100px;"></view>
  </view>
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const data = reactive({
      menu: [
        {
          title: "书架",
          icon: "miniApp/sj.png",
          url: "ebook/bookstore/"
        },
        {
          title: "更新",
          icon: "miniApp/gx.png",
          url: "ebook/more/addtime/"
        },
        {
          title: "分类",
          icon: "miniApp/fl.png",
          url: "ebook/shaixuan/"
        },
        {
          title: "排行榜",
          icon: "miniApp/ph.png",
          url: "plugin.php?id=jameson_read&contrl=touch&act=paihang"
        },
        {
          title: "已完结",
          icon: "miniApp/ywj.png",
          url: "ebook/more/end/"
        }
      ],
      historys: [],
      paihang: {
        cuigengpiao: [],
        end: [],
        haoping: [],
        sell: [],
        shoucang: [],
        tuijian: [],
        tuijianpiao: [],
        update: [],
        yuedu: [],
        yuepiao: [],
        zuixin: []
      },
      active: "tuijian",
      zhuanti: [],
      catedata: [],
      isLogin: false
    });
    const getImageUrl = url => {
      if (url.indexOf("http") == 0) {
        return url;
      }
      return (
        "https://ebookimage.chinulture.com" +
        (url.indexOf("/") == 0 ? "" : "/") +
        url
      );
    };
    const getUrl = url => {
      if (url.indexOf("http") == 0) {
        return url;
      }
      return (
        "https://wwww.chinulture.com" + (url.indexOf("/") == 0 ? "" : "/") + url
      );
    };
    const open = url => {
      if (!data.isLogin) {
        if (process.env.TARO_ENV == "tt") {
          ttLogin({}, (data, success) => {
            if (success) {
              data.isLogin = true;
              Taro.navigateTo({
                url:
                  "/pages/_ym/web?url=" +
                  encodeURIComponent(
                    "https://wwww.chinulture.com/plugin.php?id=yimen_app:tt&code=" +
                      data.code +
                      "&_back=" +
                      encodeURIComponent(getUrl(url))
                  )
              });
            } else {
              Taro.navigateTo({
                url: "/pages/_ym/web?url=" + encodeURIComponent(getUrl(url))
              });
            }
          });
        }
        return;
      }
      Taro.navigateTo({
        url: "/pages/_ym/web?url=" + encodeURIComponent(getUrl(url))
      });
    };
    const goBook = id => {
      open("ebook/book/" + id);
    };
    const ttLogin = (data, callback) => {
      var opt = Object.assign(data || {}, {
        complete(res) {
          const success = !!res.code;
          callback(
            {
              code: res.code || "",
              anonymousCode: res.anonymousCode || "",
              isLogin: res.isLogin,
              error: success ? "" : res.errMsg || "登录失败"
            },
            success
          );
        },
        fail(...tes) {
          console.log(...tes);
        }
      });
      //console.log(opt);
      tt.login(opt);
    };

    onMounted(() => {
      Taro.request({
        url: "https://wwww.chinulture.com/ebook/?__ajax=1",
        header: {
          "content-type": "application/json" // 默认值
        },
        success: function(res) {
          if (res.statusCode == 200) {
            data.paihang = res.data.paihang;
            data.zhuanti = res.data.zhuanti;
            data.catedata = res.data.catedata;
          }
        }
      });
      if (process.env.TARO_ENV == "tt") {
        //_is_login
        Taro.request({
          url:
            "https://wwww.chinulture.com/plugin.php?id=yimen_app:tt&_is_login=1",
          header: {
            "content-type": "application/json" // 默认值
          },
          success: function(res) {
            if (res.statusCode == 200 && !res.data.code) {
            }
          }
        });
      }
    });
    return { data, open, getUrl, getImageUrl, goBook };
  }
};
</script>

<style>
.main {
  background: #f8f8f8;
  font-size: 24px;
}

.header-search {
  height: 90px;
  display: flex;
  text-align: center;
}

.header-image {
  width: 90px;
  height: 90px;
}

.header-image image {
  object-fit: cover;
  width: 62px;
  height: 48px;
  margin: 21px auto;
}

.header-search .search-box {
  flex: 1;
  margin: 10px;
  line-height: 70px;
  background: #fff;
  color: #9b9b9b;
}

.header-search .search-box text {
  padding-left: 30px;
}

.menu {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
  background: #fff;
  height: 180px;
}

.menu-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.menu-icon image {
  width: 40px;
  height: 32px;
}

.menu-text {
  padding-top: 20px;
}

.book-list {
  margin-top: 20px;
  background: #fff;
}

.c777 {
  color: #777;
}

.list-header {
  padding: 10px;
}

.header-line {
  padding-left: 30px;
  padding-right: 10px;
  color: #b58a5d;
  font-weight: 700;
}

.header-title {
  font-weight: 700;
}

.header-tip {
  padding-left: 20px;
  color: #999;
  font-weight: 400;
}

.list-choose {
  display: flex;
  border: 1px solid #b58a5d;
  height: 60px;
  margin: 10px;
  line-height: 60px;
  color: #b58a5d;
}

.choose-item {
  flex: 1;
  text-align: center;
}

.choose-item.active {
  background: #b58a5d;
  color: #fff;
}

.choose-item:nth-child(3) {
  border-left: 1px solid #b58a5d;
}

.choose-item:nth-child(2) {
  border-left: 1px solid #b58a5d;
}

.scroll-view_H {
  white-space: nowrap;
}

.scroll-item {
  display: inline-block;
  width: 168px;
  height: 310px;
  padding-top: 20px;
  font-size: 24px;
  margin-right: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.scroll-item:first-child {
  margin-left: 20px;
}

.scroll-image {
  margin: 3px 8px;
  width: 160px;
  height: 240px;
}

.scroll-title {
  color: #000;
  overflow: hidden;
  height: 32px;
}

.scroll-info {
  width: 60px;
  height: 6px;
  margin: 6px auto;
  background: #ccc;
  border-radius: 2px;
}

.scroll-index {
  background: #ff6600;
  width: 12px;
  height: 6px;
  border-radius: 6px;
}
</style>
